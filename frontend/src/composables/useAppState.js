/**
 * 应用状态管理 Composable
 * 提供应用级别的状态管理功能
 */

import { computed, watch, onMounted, onUnmounted } from "vue";
import { useStore } from "@/stores";

/**
 * 应用状态管理 Hook
 */
export function useAppState() {
  const store = useStore();

  // 自动同步定时器
  let syncTimer = null;

  /**
   * 初始化应用状态
   */
  const initAppState = async () => {
    await store.initApp();

    // 如果已登录且在线，启动自动同步
    if (store.isAuthenticated.value && store.isOnline.value) {
      startAutoSync();
    }
  };

  /**
   * 启动自动同步
   */
  const startAutoSync = () => {
    if (!store.state.settings.autoRefresh) return;

    if (syncTimer) {
      clearInterval(syncTimer);
    }

    syncTimer = setInterval(() => {
      if (store.canSync.value) {
        store.syncData();
      }
    }, store.state.settings.refreshInterval);
  };

  /**
   * 停止自动同步
   */
  const stopAutoSync = () => {
    if (syncTimer) {
      clearInterval(syncTimer);
      syncTimer = null;
    }
  };

  /**
   * 切换侧边栏折叠状态
   */
  const toggleSidebar = () => {
    const collapsed = !store.state.settings.sidebarCollapsed;
    store.mutations.setSidebarCollapsed(collapsed);
  };

  /**
   * 更新应用设置
   */
  const updateSettings = (newSettings) => {
    store.mutations.updateSettings(newSettings);

    // 如果自动刷新设置改变，重新启动同步
    if ("autoRefresh" in newSettings || "refreshInterval" in newSettings) {
      if (newSettings.autoRefresh && store.isAuthenticated.value) {
        startAutoSync();
      } else {
        stopAutoSync();
      }
    }
  };

  /**
   * 处理网络状态变化
   */
  const handleNetworkChange = () => {
    if (store.isOnline.value && store.isAuthenticated.value) {
      // 网络恢复时同步数据
      store.syncData();
      startAutoSync();
    } else {
      stopAutoSync();
    }
  };

  /**
   * 处理认证状态变化
   */
  const handleAuthChange = () => {
    if (store.isAuthenticated.value) {
      startAutoSync();
    } else {
      stopAutoSync();
    }
  };

  // 监听网络状态变化
  watch(() => store.isOnline.value, handleNetworkChange);

  // 监听认证状态变化
  watch(() => store.isAuthenticated.value, handleAuthChange);

  // 组件挂载时初始化
  onMounted(() => {
    initAppState();
  });

  // 组件卸载时清理
  onUnmounted(() => {
    stopAutoSync();
  });

  return {
    // 状态
    isLoading: store.isLoading,
    isOnline: store.isOnline,
    isAuthenticated: store.isAuthenticated,
    user: computed(() => store.state.user),
    settings: computed(() => store.state.settings),
    notifications: computed(() => store.state.notifications),
    unreadCount: store.unreadCount,

    // 方法
    initAppState,
    toggleSidebar,
    updateSettings,
    startAutoSync,
    stopAutoSync,

    // 通知方法
    showSuccess: store.showSuccess,
    showError: store.showError,
    showWarning: store.showWarning,
    showInfo: store.showInfo,

    // 直接访问store
    store,
  };
}

/**
 * 通知管理 Hook
 */
export function useNotifications() {
  const store = useStore();

  /**
   * 添加通知
   */
  const addNotification = (notification) => {
    return store.showNotification(notification);
  };

  /**
   * 移除通知
   */
  const removeNotification = (id) => {
    store.mutations.removeNotification(id);
  };

  /**
   * 标记通知为已读
   */
  const markAsRead = (id) => {
    store.mutations.markNotificationRead(id);
  };

  /**
   * 清空所有通知
   */
  const clearAll = () => {
    store.mutations.clearNotifications();
  };

  /**
   * 标记所有通知为已读
   */
  const markAllAsRead = () => {
    store.state.notifications.forEach((notification) => {
      notification.read = true;
    });
  };

  return {
    // 状态
    notifications: computed(() => store.state.notifications),
    unreadNotifications: store.unreadNotifications,
    unreadCount: store.unreadCount,

    // 方法
    addNotification,
    removeNotification,
    markAsRead,
    clearAll,
    markAllAsRead,

    // 快捷方法
    success: store.showSuccess,
    error: store.showError,
    warning: store.showWarning,
    info: store.showInfo,
  };
}

/**
 * 加载状态管理 Hook
 */
export function useLoading() {
  const store = useStore();

  /**
   * 设置加载状态
   */
  const setLoading = (key, loading) => {
    store.mutations.setLoading(key, loading);
  };

  /**
   * 创建加载包装器
   */
  const withLoading = (key, asyncFn) => {
    return async (...args) => {
      setLoading(key, true);
      try {
        return await asyncFn(...args);
      } finally {
        setLoading(key, false);
      }
    };
  };

  return {
    // 状态
    loading: computed(() => store.state.loading),
    isLoading: store.isLoading,

    // 方法
    setLoading,
    withLoading,
  };
}

/**
 * 缓存管理 Hook
 */
export function useCache() {
  const store = useStore();

  /**
   * 设置缓存数据
   */
  const setCache = (key, data) => {
    store.mutations.setCacheData(key, data);
  };

  /**
   * 获取缓存数据
   */
  const getCache = (key) => {
    return store.state.cache[key];
  };

  /**
   * 清空缓存
   */
  const clearCache = () => {
    store.mutations.clearCache();
  };

  /**
   * 检查缓存是否过期
   */
  const isCacheValid = (maxAge = 10 * 60 * 1000) => {
    const age = store.cacheAge.value;
    return age !== null && age <= maxAge;
  };

  return {
    // 状态
    cache: computed(() => store.state.cache),
    cacheAge: store.cacheAge,
    isCacheStale: store.isCacheStale,

    // 方法
    setCache,
    getCache,
    clearCache,
    isCacheValid,
  };
}

export default useAppState;
