/**
 * 数据库迁移：为通知表添加'contract_transferred'通知类型
 * 支持合同流转到市局的通知
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const DB_PATH = path.join(__dirname, '..', 'database.sqlite');

// 执行SQL查询的辅助函数
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
    db.close();
  });
}

// 检查表是否存在
function checkTableExists(tableName) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [tableName],
      (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(!!row);
        }
      }
    );
    db.close();
  });
}

// 主迁移函数
async function migrate() {
  try {
    console.log('🔄 开始执行迁移：添加合同流转通知类型...');

    // 检查notifications表是否存在
    const notificationsExists = await checkTableExists('notifications');
    if (!notificationsExists) {
      console.log('❌ notifications表不存在，跳过迁移');
      return;
    }

    // SQLite不支持直接修改CHECK约束，需要重建表
    console.log('📋 重建notifications表以添加新通知类型约束...');

    // 1. 创建新表
    await runQuery(`
      CREATE TABLE notifications_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('contract_submitted', 'contract_approved', 'contract_rejected', 'contract_assigned', 'contract_transferred', 'system_notice')),
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        is_read BOOLEAN DEFAULT 0,
        related_id INTEGER,
        related_type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        read_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);
    console.log('✅ 创建新的notifications表');

    // 2. 复制数据
    await runQuery(`
      INSERT INTO notifications_new (
        id, user_id, type, title, content, is_read, related_id, related_type,
        created_at, read_at
      )
      SELECT 
        id, user_id, type, title, content, is_read, related_id, related_type,
        created_at, read_at
      FROM notifications
    `);
    console.log('✅ 复制现有数据到新表');

    // 3. 删除旧表
    await runQuery('DROP TABLE notifications');
    console.log('✅ 删除旧的notifications表');

    // 4. 重命名新表
    await runQuery('ALTER TABLE notifications_new RENAME TO notifications');
    console.log('✅ 重命名新表为notifications');

    // 5. 重建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)');
    console.log('✅ 重建表索引');

    // 6. 记录迁移执行
    await runQuery(`
      INSERT OR IGNORE INTO migrations (migration_name, version) 
      VALUES ('004_add_contract_transferred_notification_type', '1.0.0')
    `);

    console.log('🎉 迁移执行完成！通知表已支持合同流转通知类型');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrate();
}

module.exports = { migrate };
