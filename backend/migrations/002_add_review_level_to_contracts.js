/**
 * 数据库迁移：为合同表添加审核级别字段
 * 添加 review_level 字段以支持县局和市局审核员的区分
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const DB_PATH = path.join(__dirname, '..', 'database.sqlite');

// 执行SQL查询的辅助函数
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
    db.close();
  });
}

// 检查字段是否存在的辅助函数
function checkColumnExists(tableName, columnName) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        const columnExists = rows.some(row => row.name === columnName);
        resolve(columnExists);
      }
    });
    db.close();
  });
}

// 主迁移函数
async function migrate() {
  try {
    console.log('🔄 开始执行迁移：添加合同审核级别字段...');

    // 检查 review_level 字段是否已存在
    const reviewLevelExists = await checkColumnExists('contracts', 'review_level');

    if (!reviewLevelExists) {
      // 添加 review_level 字段
      await runQuery(`
        ALTER TABLE contracts 
        ADD COLUMN review_level TEXT DEFAULT 'county_reviewer' 
        CHECK (review_level IN ('county_reviewer', 'city_reviewer'))
      `);
      console.log('✅ 添加 review_level 字段到 contracts 表');
    } else {
      console.log('⚠️  review_level 字段已存在，跳过添加');
    }

    // 为现有数据设置默认审核级别
    const result = await runQuery(`
      UPDATE contracts 
      SET review_level = 'county_reviewer' 
      WHERE review_level IS NULL
    `);

    if (result.changes > 0) {
      console.log(`✅ 更新了 ${result.changes} 条现有合同记录的审核级别`);
    }

    // 创建索引以提高查询性能
    try {
      await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_review_level ON contracts(review_level)');
      console.log('✅ 创建 review_level 字段索引');
    } catch (indexError) {
      console.log('⚠️  索引可能已存在，跳过创建');
    }

    // 记录迁移执行
    await runQuery(`
      INSERT OR IGNORE INTO migrations (migration_name, version) 
      VALUES ('002_add_review_level_to_contracts', '1.0.0')
    `);

    console.log('🎉 迁移执行完成！');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrate();
}

module.exports = { migrate };
