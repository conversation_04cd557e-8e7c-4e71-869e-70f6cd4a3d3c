{"name": "hetong-frontend", "version": "1.0.0", "description": "合同审核系统前端界面", "type": "module", "scripts": {"dev": "vite", "dev:clean": "npm run clean:cache && vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "clean:cache": "rm -rf node_modules/.vite && rm -rf dist", "clean:all": "rm -rf node_modules node_modules/.vite dist package-lock.json", "reinstall": "npm run clean:all && npm install", "dev:force": "vite --force"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "cropperjs": "^1.6.1", "echarts": "^5.6.0", "element-plus": "^2.4.4", "pdfjs-dist": "^5.3.93", "pinia": "^3.0.3", "vue": "^3.3.8", "vue-echarts": "^7.0.3", "vue-pdf-embed": "^1.2.1", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "playwright": "^1.54.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "sass-embedded": "^1.89.2", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "vite": "^5.0.0"}, "keywords": ["vue3", "element-plus", "contract", "review", "system"], "author": "Contract Review System", "license": "MIT"}