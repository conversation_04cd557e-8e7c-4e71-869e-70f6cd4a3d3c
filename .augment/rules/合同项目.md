---
type: "always_apply"
description: "Example description"
---
## 核心工作流程

### 🔍 信息处理流程
- **知识获取**: 通过 context7 MCP 获取最新技术资讯
- **思考模式**: 内部使用英语确保逻辑准确
- **交互语言**: 统一使用中文回复

### 🛠️ 开发测试体系
- **调试工具**: Playwright MCP 无头模式
- **覆盖范围**: 全栈调试、测试、问题诊断

### 📋 任务管理系统
- **统一调度**: taskmaster 管理所有工作流
- **执行优化**: 智能优先级排序

### ⚡ 并发开发命令
```bash


concurrently "cd backend && npm start" "cd frontend && npm run dev" --names "backend,frontend" --prefix name


```

## 服务承诺
✅ 工具协同，形成完整闭环
✅ 用户需求导向，高质量技术支持
✅ 持续学习优化，提升工作效率