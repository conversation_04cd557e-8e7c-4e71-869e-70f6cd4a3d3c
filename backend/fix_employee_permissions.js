/**
 * 修复员工删除权限的脚本
 * 为员工角色添加 contract:delete 权限
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'database.sqlite');

async function fixEmployeePermissions() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ 连接数据库失败:', err.message);
        reject(err);
        return;
      }
      console.log('✅ 连接到数据库');
    });

    // 查询员工角色ID
    db.get("SELECT id FROM roles WHERE name = 'employee'", (err, employeeRole) => {
      if (err) {
        console.error('❌ 查询员工角色失败:', err.message);
        db.close();
        reject(err);
        return;
      }

      if (!employeeRole) {
        console.error('❌ 未找到员工角色');
        db.close();
        reject(new Error('Employee role not found'));
        return;
      }

      console.log('📋 员工角色ID:', employeeRole.id);

      // 查询 contract:delete 权限ID
      db.get("SELECT id FROM permissions WHERE name = 'contract:delete'", (err, deletePermission) => {
        if (err) {
          console.error('❌ 查询删除权限失败:', err.message);
          db.close();
          reject(err);
          return;
        }

        if (!deletePermission) {
          console.error('❌ 未找到删除权限');
          db.close();
          reject(new Error('Delete permission not found'));
          return;
        }

        console.log('📋 删除权限ID:', deletePermission.id);

        // 检查是否已经存在关联
        db.get(
          "SELECT * FROM role_permissions WHERE role_id = ? AND permission_id = ?",
          [employeeRole.id, deletePermission.id],
          (err, existing) => {
            if (err) {
              console.error('❌ 检查权限关联失败:', err.message);
              db.close();
              reject(err);
              return;
            }

            if (existing) {
              console.log('✅ 员工角色已经拥有删除权限');
              db.close();
              resolve();
              return;
            }

            // 添加权限关联
            db.run(
              "INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)",
              [employeeRole.id, deletePermission.id],
              function(err) {
                if (err) {
                  console.error('❌ 添加权限关联失败:', err.message);
                  db.close();
                  reject(err);
                  return;
                }

                console.log('✅ 成功为员工角色添加删除权限');
                
                // 验证权限
                db.all(`
                  SELECT p.name as permission_name
                  FROM permissions p
                  INNER JOIN role_permissions rp ON p.id = rp.permission_id
                  WHERE rp.role_id = ?
                  ORDER BY p.name
                `, [employeeRole.id], (err, permissions) => {
                  if (err) {
                    console.error('❌ 验证权限失败:', err.message);
                  } else {
                    console.log('📋 员工角色当前权限:');
                    permissions.forEach(p => console.log(`  - ${p.permission_name}`));
                  }
                  
                  db.close();
                  resolve();
                });
              }
            );
          }
        );
      });
    });
  });
}

// 执行修复
fixEmployeePermissions()
  .then(() => {
    console.log('🎉 权限修复完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 权限修复失败:', error.message);
    process.exit(1);
  });
