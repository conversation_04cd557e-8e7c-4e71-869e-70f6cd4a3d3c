/**
 * 数据库迁移：为合同表添加'待市局审核'状态
 * 更新合同状态约束以支持县局到市局的审核流转
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const DB_PATH = path.join(__dirname, '..', 'database.sqlite');

// 执行SQL查询的辅助函数
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
    db.close();
  });
}

// 检查表是否存在
function checkTableExists(tableName) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [tableName],
      (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(!!row);
        }
      }
    );
    db.close();
  });
}

// 主迁移函数
async function migrate() {
  try {
    console.log('🔄 开始执行迁移：添加待市局审核状态...');

    // 检查contracts表是否存在
    const contractsExists = await checkTableExists('contracts');
    if (!contractsExists) {
      console.log('❌ contracts表不存在，跳过迁移');
      return;
    }

    // SQLite不支持直接修改CHECK约束，需要重建表
    console.log('📋 重建contracts表以添加新状态约束...');

    // 1. 创建新表
    await runQuery(`
      CREATE TABLE contracts_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        serial_number TEXT UNIQUE NOT NULL,
        submitter_id INTEGER NOT NULL,
        reviewer_id INTEGER,
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'pending_city_review', 'approved', 'rejected')),
        submit_note TEXT,
        review_comment TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        reviewed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        review_level TEXT DEFAULT 'county_reviewer' CHECK (review_level IN ('county_reviewer', 'city_reviewer')),
        FOREIGN KEY (submitter_id) REFERENCES users(id),
        FOREIGN KEY (reviewer_id) REFERENCES users(id)
      )
    `);
    console.log('✅ 创建新的contracts表');

    // 2. 复制数据
    await runQuery(`
      INSERT INTO contracts_new (
        id, serial_number, submitter_id, reviewer_id, filename, file_path, file_size,
        status, submit_note, review_comment, submitted_at, reviewed_at, 
        created_at, updated_at, review_level
      )
      SELECT 
        id, serial_number, submitter_id, reviewer_id, filename, file_path, file_size,
        status, submit_note, review_comment, submitted_at, reviewed_at,
        created_at, updated_at, 
        COALESCE(review_level, 'county_reviewer') as review_level
      FROM contracts
    `);
    console.log('✅ 复制现有数据到新表');

    // 3. 删除旧表
    await runQuery('DROP TABLE contracts');
    console.log('✅ 删除旧的contracts表');

    // 4. 重命名新表
    await runQuery('ALTER TABLE contracts_new RENAME TO contracts');
    console.log('✅ 重命名新表为contracts');

    // 5. 重建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_serial ON contracts(serial_number)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_submitter ON contracts(submitter_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_reviewer ON contracts(reviewer_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_review_level ON contracts(review_level)');
    console.log('✅ 重建表索引');

    // 6. 记录迁移执行
    await runQuery(`
      INSERT OR IGNORE INTO migrations (migration_name, version) 
      VALUES ('003_add_pending_city_review_status', '1.0.0')
    `);

    console.log('🎉 迁移执行完成！合同表已支持待市局审核状态');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrate();
}

module.exports = { migrate };
