/**
 * Logo响应式测试脚本
 * 测试登录页面Logo在不同分辨率和浏览器下的显示效果
 */

const { chromium, firefox, webkit } = require('playwright');

// 测试的分辨率配置
const viewports = [
  { name: '桌面大屏', width: 1920, height: 1080 },
  { name: '桌面标准', width: 1366, height: 768 },
  { name: '平板横屏', width: 1024, height: 768 },
  { name: '平板竖屏', width: 768, height: 1024 },
  { name: '手机大屏', width: 414, height: 896 },
  { name: '手机标准', width: 375, height: 667 },
  { name: '手机小屏', width: 320, height: 568 }
];

// 浏览器配置
const browsers = [
  { name: 'Chromium', engine: chromium },
  { name: 'Firefox', engine: firefox },
  { name: 'WebKit', engine: webkit }
];

async function testLogoResponsive() {
  console.log('🚀 开始Logo响应式测试...\n');
  
  const results = [];
  
  for (const browserConfig of browsers) {
    console.log(`📱 测试浏览器: ${browserConfig.name}`);
    
    const browser = await browserConfig.engine.launch({ headless: false });
    
    for (const viewport of viewports) {
      console.log(`  📐 测试分辨率: ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      const context = await browser.newContext({
        viewport: { width: viewport.width, height: viewport.height }
      });
      
      const page = await context.newPage();
      
      try {
        // 访问登录页面
        await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle' });
        
        // 等待Logo加载
        await page.waitForSelector('.company-logo', { timeout: 5000 });
        
        // 获取Logo元素信息
        const logoInfo = await page.evaluate(() => {
          const logo = document.querySelector('.company-logo');
          if (!logo) return null;
          
          const rect = logo.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(logo);
          
          return {
            width: rect.width,
            height: rect.height,
            maxWidth: computedStyle.maxWidth,
            minWidth: computedStyle.minWidth,
            objectFit: computedStyle.objectFit,
            isVisible: rect.width > 0 && rect.height > 0
          };
        });
        
        if (logoInfo) {
          const result = {
            browser: browserConfig.name,
            viewport: viewport.name,
            resolution: `${viewport.width}x${viewport.height}`,
            logoWidth: Math.round(logoInfo.width),
            logoHeight: Math.round(logoInfo.height),
            maxWidth: logoInfo.maxWidth,
            minWidth: logoInfo.minWidth,
            objectFit: logoInfo.objectFit,
            isVisible: logoInfo.isVisible,
            aspectRatio: (logoInfo.width / logoInfo.height).toFixed(2)
          };
          
          results.push(result);
          
          console.log(`    ✅ Logo尺寸: ${result.logoWidth}x${result.logoHeight}px, 宽高比: ${result.aspectRatio}`);
        } else {
          console.log(`    ❌ Logo未找到`);
        }
        
        // 短暂等待以便观察
        await page.waitForTimeout(1000);
        
      } catch (error) {
        console.log(`    ❌ 测试失败: ${error.message}`);
      }
      
      await context.close();
    }
    
    await browser.close();
    console.log('');
  }
  
  // 生成测试报告
  generateReport(results);
}

function generateReport(results) {
  console.log('📊 测试报告生成中...\n');
  
  // 按分辨率分组统计
  const byViewport = {};
  results.forEach(result => {
    if (!byViewport[result.viewport]) {
      byViewport[result.viewport] = [];
    }
    byViewport[result.viewport].push(result);
  });
  
  console.log('📋 各分辨率下Logo尺寸统计:');
  console.log('=' .repeat(80));
  console.log('分辨率\t\t\t平均宽度\t平均高度\t宽高比\t\t兼容性');
  console.log('-'.repeat(80));
  
  Object.entries(byViewport).forEach(([viewport, viewportResults]) => {
    const avgWidth = Math.round(viewportResults.reduce((sum, r) => sum + r.logoWidth, 0) / viewportResults.length);
    const avgHeight = Math.round(viewportResults.reduce((sum, r) => sum + r.logoHeight, 0) / viewportResults.length);
    const avgRatio = (avgWidth / avgHeight).toFixed(2);
    const compatibility = viewportResults.every(r => r.isVisible) ? '✅ 完全兼容' : '⚠️  部分问题';
    
    console.log(`${viewport.padEnd(20)}\t${avgWidth}px\t\t${avgHeight}px\t\t${avgRatio}\t\t${compatibility}`);
  });
  
  console.log('\n🎯 测试结论:');
  const allVisible = results.every(r => r.isVisible);
  const aspectRatiosConsistent = new Set(results.map(r => r.aspectRatio)).size <= 2; // 允许小幅差异
  
  if (allVisible && aspectRatiosConsistent) {
    console.log('✅ Logo响应式设计测试通过！');
    console.log('   - 所有分辨率下Logo均正常显示');
    console.log('   - 宽高比保持一致，无拉伸变形');
    console.log('   - 跨浏览器兼容性良好');
  } else {
    console.log('⚠️  Logo响应式设计需要优化：');
    if (!allVisible) console.log('   - 部分分辨率下Logo显示异常');
    if (!aspectRatiosConsistent) console.log('   - 宽高比不一致，可能存在拉伸');
  }
}

// 运行测试
testLogoResponsive().catch(console.error);
