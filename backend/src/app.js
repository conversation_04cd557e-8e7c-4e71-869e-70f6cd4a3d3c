/**
 * 合同审核系统后端应用入口
 * Express.js 服务器配置和启动
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const { SYSTEM_CONFIG, HTTP_STATUS } = require('./utils/constants');
const { initEmailService } = require('./utils/email');

// 创建 Express 应用
const app = express();

// 安全中间件 - 开发环境放宽限制以支持iframe
if (SYSTEM_CONFIG.IS_DEVELOPMENT) {
  app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: false, // 开发环境禁用CSP以避免iframe问题
    frameguard: false, // 开发环境禁用X-Frame-Options以允许iframe
  }));
} else {
  app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "blob:"],
        fontSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'self'"],
        frameAncestors: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        workerSrc: ["'self'", "blob:"],
        childSrc: ["'self'", "blob:"],
        formAction: ["'self'"],
        upgradeInsecureRequests: [],
      },
    },
    frameguard: { action: 'sameorigin' }, // 生产环境允许同源iframe
  }));
}

// 动态CORS配置
const getAllowedOrigins = () => {
  const origins = [
    'http://localhost:5173',
    'http://localhost:8080',
    'http://127.0.0.1:5173',
    // 生产环境前端地址
    'http://*************:5173'
  ]

  // 添加环境变量中的前端URL
  if (process.env.FRONTEND_URL) {
    origins.push(process.env.FRONTEND_URL)
  }

  // 开发环境允许所有localhost和127.0.0.1的端口
  if (process.env.NODE_ENV === 'development') {
    origins.push(/^http:\/\/localhost:\d+$/)
    origins.push(/^http:\/\/127\.0\.0\.1:\d+$/)
  }

  return origins.filter(Boolean)
}

// CORS 配置
app.use(cors({
  origin: getAllowedOrigins(),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 请求日志
if (SYSTEM_CONFIG.IS_DEVELOPMENT) {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 请求限制 - 针对不同环境使用不同的限制策略
const limiter = rateLimit({
  windowMs: SYSTEM_CONFIG.IS_DEVELOPMENT ? 1 * 60 * 1000 : 15 * 60 * 1000, // 开发环境1分钟，生产环境15分钟
  max: SYSTEM_CONFIG.IS_DEVELOPMENT ? 1000 : 100, // 开发环境1000个请求，生产环境100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true, // 返回速率限制信息在 `RateLimit-*` 头中
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头
});

// 应用速率限制
app.use('/api/', limiter);

// 解析请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 用于文件下载和预览
app.use('/uploads', express.static(path.join(__dirname, '../uploads'), {
  maxAge: '1d', // 1天缓存
  etag: true,
  lastModified: true,
  setHeaders: (res, path, stat) => {
    // 根据文件类型设置不同的缓存策略
    if (path.match(/\.(pdf|doc|docx|xls|xlsx)$/i)) {
      res.set('Cache-Control', 'public, max-age=86400'); // 文档文件1天缓存
    } else if (path.match(/\.(jpg|jpeg|png|gif|ico)$/i)) {
      res.set('Cache-Control', 'public, max-age=604800'); // 图片文件7天缓存
    } else {
      res.set('Cache-Control', 'public, max-age=3600'); // 其他文件1小时缓存
    }
  }
}));

// API 路由
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/roles', require('./routes/roles'));
app.use('/api/contracts', require('./routes/contracts'));
app.use('/api/files', require('./routes/files'));
app.use('/api/admin', require('./routes/admin'));
app.use('/api/dashboard', require('./routes/dashboard'));
app.use('/api/logs', require('./routes/logs'));
app.use('/api/audit', require('./routes/audit'));
app.use('/api/search', require('./routes/search'));
app.use('/api/export', require('./routes/export'));
app.use('/api/chunk-upload', require('./routes/chunkUpload'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/statistics', require('./routes/statistics'));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: SYSTEM_CONFIG.VERSION,
    environment: SYSTEM_CONFIG.NODE_ENV
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: `${SYSTEM_CONFIG.APP_NAME} API`,
    version: SYSTEM_CONFIG.VERSION,
    description: SYSTEM_CONFIG.DESCRIPTION,
    docs: '/api/docs'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(HTTP_STATUS.NOT_FOUND).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);

  // 数据库错误
  if (err.code === 'SQLITE_CONSTRAINT') {
    return res.status(HTTP_STATUS.CONFLICT).json({
      success: false,
      message: '数据约束冲突',
      error: SYSTEM_CONFIG.IS_DEVELOPMENT ? err.message : undefined
    });
  }

  // 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(HTTP_STATUS.BAD_REQUEST).json({
      success: false,
      message: '文件大小超过限制'
    });
  }

  // JWT 错误
  if (err.name === 'JsonWebTokenError') {
    return res.status(HTTP_STATUS.UNAUTHORIZED).json({
      success: false,
      message: '无效的访问令牌'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(HTTP_STATUS.UNAUTHORIZED).json({
      success: false,
      message: '访问令牌已过期'
    });
  }

  // 默认错误响应
  res.status(err.status || HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
    success: false,
    message: err.message || '服务器内部错误',
    error: SYSTEM_CONFIG.IS_DEVELOPMENT ? err.stack : undefined
  });
});

// 启动服务器
const PORT = SYSTEM_CONFIG.PORT;
const HOST = SYSTEM_CONFIG.HOST;

app.listen(PORT, HOST, () => {
  // 使用DISPLAY_HOST来显示地址，如果没有则使用HOST
  const displayHost = process.env.DISPLAY_HOST || HOST;

  console.log(`
🚀 ${SYSTEM_CONFIG.APP_NAME} 后端服务启动成功！
📍 服务地址: http://${displayHost}:${PORT}
🌍 环境: ${SYSTEM_CONFIG.NODE_ENV}
📝 API文档: http://${displayHost}:${PORT}/api/docs
💾 数据库: SQLite
📁 上传目录: ${path.join(__dirname, '../uploads')}
${SYSTEM_CONFIG.IS_DEVELOPMENT ? '🔧 开发环境热重载已启用' : ''}
  `);

  // 初始化邮件服务
  initEmailService();

  // 启动缓存预热
  const { warmupCache } = require('./middleware/cache');
  setTimeout(() => {
    warmupCache();
  }, 2000); // 延迟2秒启动，确保数据库连接稳定
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  process.exit(0);
});

module.exports = app;
