/**
 * 数据导出路由
 * 提供Excel和PDF格式的数据导出功能
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { requireRole } = require('../middleware/permission');
const { ContractModel, UserModel } = require('../utils/database');
const { getOperationLogs } = require('../utils/logger');
const { ExcelExportService, PDFExportService } = require('../utils/export');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, USER_ROLES } = require('../utils/constants');

const router = express.Router();

/**
 * 导出合同数据
 * GET /api/export/contracts
 */
router.get('/contracts',
  authenticateToken,
  [
    query('format').isIn(['excel', 'pdf']).withMessage('格式必须是excel或pdf'),
    query('status').optional().isIn(['pending', 'approved', 'rejected']).withMessage('状态无效'),
    query('submitterId').optional().isInt({ min: 1 }).withMessage('提交人ID必须是正整数'),
    query('reviewerId').optional().isInt({ min: 1 }).withMessage('审核人ID必须是正整数'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        format,
        status,
        submitterId,
        reviewerId,
        startDate,
        endDate
      } = req.query;

      // 构建搜索参数
      const searchParams = {
        status,
        submitterId: submitterId ? parseInt(submitterId) : undefined,
        reviewerId: reviewerId ? parseInt(reviewerId) : undefined,
        startDate,
        endDate,
        page: 1,
        limit: 10000, // 导出时获取更多数据
        currentUserId: req.user.id,
        userRole: req.user.role
      };

      // 权限检查：普通员工只能导出自己的合同
      if (req.user.role === USER_ROLES.EMPLOYEE) {
        searchParams.submitterId = req.user.id;
      }

      // 审核员只能导出分配给自己的合同
      if (req.user.role === USER_ROLES.REVIEWER) {
        searchParams.reviewerId = req.user.id;
      }

      // 获取合同数据
      const result = await ContractModel.advancedSearch(searchParams);
      const contracts = result.contracts;

      if (contracts.length === 0) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('没有找到符合条件的合同数据', HTTP_STATUS.NOT_FOUND)
        );
      }

      let buffer;
      let filename;
      let contentType;

      if (format === 'excel') {
        buffer = await ExcelExportService.exportContracts(contracts, {
          title: '合同数据导出',
          sheetName: '合同列表'
        });
        filename = `contracts_${new Date().toISOString().split('T')[0]}.xlsx`;
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else {
        buffer = await PDFExportService.exportContracts(contracts, {
          title: '合同数据导出报告'
        });
        filename = `contracts_${new Date().toISOString().split('T')[0]}.pdf`;
        contentType = 'application/pdf';
      }

      // 设置响应头
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      // 发送文件
      res.send(buffer);

    } catch (error) {
      console.error('导出合同数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('导出失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 导出用户数据（管理员专用）
 * GET /api/export/users
 */
router.get('/users',
  authenticateToken,
  requireRole(['admin']),
  [
    query('format').isIn(['excel', 'pdf']).withMessage('格式必须是excel或pdf'),
    query('role').optional().isIn(['employee', 'reviewer', 'admin']).withMessage('角色无效'),
    query('status').optional().isIn(['active', 'banned']).withMessage('状态无效'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        format,
        role,
        status,
        startDate,
        endDate
      } = req.query;

      // 构建搜索参数
      const searchParams = {
        role,
        status,
        startDate,
        endDate,
        page: 1,
        limit: 10000 // 导出时获取更多数据
      };

      // 获取用户数据
      const result = await UserModel.advancedSearch(searchParams);
      const users = result.users;

      if (users.length === 0) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('没有找到符合条件的用户数据', HTTP_STATUS.NOT_FOUND)
        );
      }

      let buffer;
      let filename;
      let contentType;

      if (format === 'excel') {
        buffer = await ExcelExportService.exportUsers(users, {
          title: '用户数据导出',
          sheetName: '用户列表'
        });
        filename = `users_${new Date().toISOString().split('T')[0]}.xlsx`;
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else {
        buffer = await PDFExportService.exportUsers(users, {
          title: '用户数据导出报告'
        });
        filename = `users_${new Date().toISOString().split('T')[0]}.pdf`;
        contentType = 'application/pdf';
      }

      // 设置响应头
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      // 发送文件
      res.send(buffer);

    } catch (error) {
      console.error('导出用户数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('导出失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 导出操作日志（管理员专用）
 * GET /api/export/logs
 */
router.get('/logs',
  authenticateToken,
  requireRole(['admin']),
  [
    query('format').isIn(['excel', 'pdf']).withMessage('格式必须是excel或pdf'),
    query('userId').optional().isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
    query('action').optional().isString().withMessage('操作类型必须是字符串'),
    query('resourceType').optional().isString().withMessage('资源类型必须是字符串'),
    query('status').optional().isIn(['success', 'failed']).withMessage('状态无效'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        format,
        userId,
        action,
        resourceType,
        status,
        startDate,
        endDate
      } = req.query;

      // 构建搜索参数
      const searchParams = {
        userId: userId ? parseInt(userId) : undefined,
        action,
        resourceType,
        status,
        startDate,
        endDate,
        page: 1,
        limit: 10000 // 导出时获取更多数据
      };

      // 获取日志数据
      const result = await getOperationLogs(searchParams);
      const logs = result.logs;

      if (logs.length === 0) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('没有找到符合条件的日志数据', HTTP_STATUS.NOT_FOUND)
        );
      }

      let buffer;
      let filename;
      let contentType;

      if (format === 'excel') {
        buffer = await ExcelExportService.exportLogs(logs, {
          title: '操作日志导出',
          sheetName: '操作日志'
        });
        filename = `logs_${new Date().toISOString().split('T')[0]}.xlsx`;
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else {
        // PDF格式的日志导出（简化版）
        buffer = Buffer.from('PDF export for logs is not implemented yet');
        filename = `logs_${new Date().toISOString().split('T')[0]}.pdf`;
        contentType = 'application/pdf';
      }

      // 设置响应头
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      // 发送文件
      res.send(buffer);

    } catch (error) {
      console.error('导出操作日志错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('导出失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 导出统计报表
 * GET /api/export/statistics
 */
router.get('/statistics',
  authenticateToken,
  requireRole(['admin', 'reviewer']),
  [
    query('format').isIn(['excel', 'pdf']).withMessage('格式必须是excel或pdf'),
    query('period').optional().isIn(['7d', '30d', '90d', '1y']).withMessage('统计周期无效')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { format, period = '30d' } = req.query;

      // 获取统计数据
      const contractStats = await ContractModel.getStats();
      const userStats = req.user.role === USER_ROLES.ADMIN ? await UserModel.getStats() : null;

      // 构建统计数据
      const statsData = {
        contractStats,
        userStats,
        period,
        generatedAt: new Date().toISOString()
      };

      let buffer;
      let filename;
      let contentType;

      if (format === 'excel') {
        // 创建统计报表Excel
        buffer = await this.createStatisticsExcel(statsData);
        filename = `statistics_${new Date().toISOString().split('T')[0]}.xlsx`;
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else {
        // 创建统计报表PDF
        buffer = await this.createStatisticsPDF(statsData);
        filename = `statistics_${new Date().toISOString().split('T')[0]}.pdf`;
        contentType = 'application/pdf';
      }

      // 设置响应头
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      // 发送文件
      res.send(buffer);

    } catch (error) {
      console.error('导出统计报表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('导出失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 创建统计报表Excel
 * @param {Object} statsData - 统计数据
 * @returns {Promise<Buffer>} Excel文件缓冲区
 */
async function createStatisticsExcel(statsData) {
  // 这里可以创建更复杂的统计报表
  // 暂时返回一个简单的实现
  return Buffer.from('Statistics Excel export is not fully implemented yet');
}

/**
 * 创建统计报表PDF
 * @param {Object} statsData - 统计数据
 * @returns {Promise<Buffer>} PDF文件缓冲区
 */
async function createStatisticsPDF(statsData) {
  // 这里可以创建更复杂的统计报表
  // 暂时返回一个简单的实现
  return Buffer.from('Statistics PDF export is not fully implemented yet');
}

module.exports = router;