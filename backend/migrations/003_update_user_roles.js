/**
 * 数据库迁移：更新用户表角色约束
 * 支持新的角色类型：county_reviewer, city_reviewer
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const DB_PATH = path.join(__dirname, '..', 'database.sqlite');

// 执行SQL查询的辅助函数
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
    db.close();
  });
}

// 获取所有数据的辅助函数
function getAllData(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
    db.close();
  });
}

// 主迁移函数
async function migrate() {
  try {
    console.log('🔄 开始执行迁移：更新用户表角色约束...');

    // 1. 备份现有用户数据
    const users = await getAllData('SELECT * FROM users');
    console.log(`📋 备份了 ${users.length} 个用户记录`);

    // 2. 创建新的用户表
    await runQuery(`
      CREATE TABLE users_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('employee', 'county_reviewer', 'city_reviewer', 'reviewer', 'admin')),
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'banned')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        avatar TEXT,
        role_id INTEGER,
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);
    console.log('✅ 创建新用户表');

    // 3. 复制数据到新表
    for (const user of users) {
      await runQuery(`
        INSERT INTO users_new (
          id, username, password, role, status, created_at, updated_at, created_by, avatar, role_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id, user.username, user.password, user.role, user.status,
        user.created_at, user.updated_at, user.created_by, user.avatar, user.role_id
      ]);
    }
    console.log('✅ 复制用户数据到新表');

    // 4. 删除旧表并重命名新表
    await runQuery('DROP TABLE users');
    await runQuery('ALTER TABLE users_new RENAME TO users');
    console.log('✅ 替换用户表');

    // 5. 重新创建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
    console.log('✅ 重新创建索引');

    // 6. 记录迁移执行
    await runQuery(`
      INSERT OR IGNORE INTO migrations (migration_name, version) 
      VALUES ('003_update_user_roles', '1.0.0')
    `);

    console.log('🎉 迁移执行完成！');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrate();
}

module.exports = { migrate };
