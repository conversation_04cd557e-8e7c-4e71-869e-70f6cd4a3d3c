#!/usr/bin/env node

/**
 * 端到端测试脚本：验证审核员预览合同功能
 * 测试县局和市局审核员是否能够成功预览合同
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const BASE_URL = 'http://localhost:3000';

// 测试账号信息
const TEST_ACCOUNTS = [
  {
    username: 'county_reviewer1',
    password: '123456',
    role: 'county_reviewer',
    name: '县局审核员1'
  },
  {
    username: 'city_reviewer1',
    password: '123456',
    role: 'city_reviewer',
    name: '市局审核员1'
  }
];

/**
 * 登录获取token
 */
async function login(username, password) {
  try {
    const curlCmd = `curl -s -X POST ${BASE_URL}/api/auth/login \\
      -H "Content-Type: application/json" \\
      -d '{"username":"${username}","password":"${password}"}'`;

    const { stdout } = await execAsync(curlCmd);
    const response = JSON.parse(stdout);

    if (response.success) {
      console.log(`✅ ${username} 登录成功`);
      return response.data.token;
    } else {
      console.log(`❌ ${username} 登录失败: ${response.message}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ ${username} 登录异常: ${error.message}`);
    return null;
  }
}

/**
 * 获取合同列表
 */
async function getContracts(token) {
  try {
    const curlCmd = `curl -s -X GET ${BASE_URL}/api/contracts \\
      -H "Authorization: Bearer ${token}"`;

    const { stdout } = await execAsync(curlCmd);
    const response = JSON.parse(stdout);

    if (response.success) {
      console.log(`✅ 获取合同列表成功，共 ${response.data.length} 份合同`);
      return response.data;
    } else {
      console.log(`❌ 获取合同列表失败: ${response.message}`);
      return [];
    }
  } catch (error) {
    console.log(`❌ 获取合同列表异常: ${error.message}`);
    return [];
  }
}

/**
 * 预览合同
 */
async function previewContract(token, contractId, contractTitle) {
  try {
    const curlCmd = `curl -s -X GET ${BASE_URL}/api/contracts/${contractId} \\
      -H "Authorization: Bearer ${token}"`;

    const { stdout } = await execAsync(curlCmd);
    const response = JSON.parse(stdout);

    if (response.success) {
      console.log(`✅ 预览合同成功: ${contractTitle} (ID: ${contractId})`);
      return true;
    } else {
      console.log(`❌ 预览合同失败: ${contractTitle} (ID: ${contractId}) - ${response.message}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 预览合同异常: ${contractTitle} (ID: ${contractId}) - ${error.message}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('🚀 开始端到端测试：审核员预览合同功能\n');

  for (const account of TEST_ACCOUNTS) {
    console.log(`\n📋 测试账号: ${account.name} (${account.username})`);
    console.log('='.repeat(50));

    // 1. 登录
    const token = await login(account.username, account.password);
    if (!token) {
      console.log(`❌ ${account.name} 测试失败：无法登录\n`);
      continue;
    }

    // 2. 获取合同列表
    const contracts = await getContracts(token);
    if (contracts.length === 0) {
      console.log(`⚠️  ${account.name} 测试跳过：没有可预览的合同\n`);
      continue;
    }

    // 3. 测试预览前3份合同
    const testContracts = contracts.slice(0, 3);
    let successCount = 0;

    for (const contract of testContracts) {
      const success = await previewContract(token, contract.id, contract.title);
      if (success) successCount++;
    }

    console.log(`\n📊 ${account.name} 测试结果: ${successCount}/${testContracts.length} 份合同预览成功`);

    if (successCount === testContracts.length) {
      console.log(`🎉 ${account.name} 测试通过！`);
    } else {
      console.log(`⚠️  ${account.name} 测试部分失败`);
    }
  }

  console.log('\n🏁 端到端测试完成');
}

// 运行测试
runTest().catch(console.error);
