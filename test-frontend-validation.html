<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端验证规则测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #409eff;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #c2e7b0;
        }
        .error {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        .info {
            background-color: #f4f4f5;
            color: #909399;
            border: 1px solid #d3d4d6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">🧪 审核表单验证规则测试</h2>
        <p>此页面模拟前端审核表单的验证逻辑，测试不同场景下的验证规则。</p>
    </div>

    <div class="test-container">
        <div class="test-case">
            <h4>测试场景1：审核通过时不填写说明</h4>
            <div class="form-group">
                <label>审核结果：</label>
                <select id="result1" onchange="updateValidation(1)">
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label>审核意见：</label>
                <textarea id="comment1" placeholder="请输入审核意见" oninput="updateValidation(1)"></textarea>
            </div>
            <button id="submit1" onclick="testSubmit(1)" disabled>提交审核结果</button>
            <div id="result1" class="result info">请先选择审核结果</div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-case">
            <h4>测试场景2：审核通过时填写少于10字符说明</h4>
            <div class="form-group">
                <label>审核结果：</label>
                <select id="result2" onchange="updateValidation(2)">
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label>审核意见：</label>
                <textarea id="comment2" placeholder="请输入审核意见" oninput="updateValidation(2)">同意</textarea>
            </div>
            <button id="submit2" onclick="testSubmit(2)" disabled>提交审核结果</button>
            <div id="result2" class="result info">请先选择审核结果</div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-case">
            <h4>测试场景3：审核驳回时不填写说明</h4>
            <div class="form-group">
                <label>审核结果：</label>
                <select id="result3" onchange="updateValidation(3)">
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label>审核意见：</label>
                <textarea id="comment3" placeholder="请输入审核意见" oninput="updateValidation(3)"></textarea>
            </div>
            <button id="submit3" onclick="testSubmit(3)" disabled>提交审核结果</button>
            <div id="result3" class="result info">请先选择审核结果</div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-case">
            <h4>测试场景4：审核驳回时填写少于10字符说明</h4>
            <div class="form-group">
                <label>审核结果：</label>
                <select id="result4" onchange="updateValidation(4)">
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label>审核意见：</label>
                <textarea id="comment4" placeholder="请输入审核意见" oninput="updateValidation(4)">不行</textarea>
            </div>
            <button id="submit4" onclick="testSubmit(4)" disabled>提交审核结果</button>
            <div id="result4" class="result info">请先选择审核结果</div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-case">
            <h4>测试场景5：审核驳回时填写10字符以上说明</h4>
            <div class="form-group">
                <label>审核结果：</label>
                <select id="result5" onchange="updateValidation(5)">
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label>审核意见：</label>
                <textarea id="comment5" placeholder="请输入审核意见" oninput="updateValidation(5)">合同内容不符合要求，请重新修改后提交</textarea>
            </div>
            <button id="submit5" onclick="testSubmit(5)" disabled>提交审核结果</button>
            <div id="result5" class="result info">请先选择审核结果</div>
        </div>
    </div>

    <script>
        // 模拟前端验证规则
        function validateForm(result, comment) {
            const errors = [];
            
            if (!result) {
                errors.push('请选择审核结果');
                return { valid: false, errors };
            }
            
            if (result === 'rejected') {
                // 审核驳回时，说明必填且至少10个字符
                if (!comment || comment.trim().length === 0) {
                    errors.push('审核驳回时必须填写审核意见');
                }
                if (comment && comment.trim().length < 10) {
                    errors.push('审核意见至少10个字符');
                }
            }
            
            // 审核通过时，说明可选，但如果填写了则检查长度
            if (comment && comment.length > 1000) {
                errors.push('审核意见不能超过1000字符');
            }
            
            return { valid: errors.length === 0, errors };
        }
        
        function updateValidation(testCase) {
            const result = document.getElementById(`result${testCase}`).value;
            const comment = document.getElementById(`comment${testCase}`).value;
            const submitBtn = document.getElementById(`submit${testCase}`);
            const resultDiv = document.getElementById(`result${testCase}`);
            
            const validation = validateForm(result, comment);
            
            if (validation.valid) {
                submitBtn.disabled = false;
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 验证通过，可以提交';
            } else {
                submitBtn.disabled = true;
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ ' + validation.errors.join(', ');
            }
        }
        
        function testSubmit(testCase) {
            const result = document.getElementById(`result${testCase}`).value;
            const comment = document.getElementById(`comment${testCase}`).value;
            const resultDiv = document.getElementById(`result${testCase}`);
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `🎉 提交成功！审核结果：${result === 'approved' ? '通过' : '拒绝'}，说明：${comment || '(无)'}`;
        }
        
        // 初始化所有测试场景
        for (let i = 1; i <= 5; i++) {
            updateValidation(i);
        }
    </script>
</body>
</html>
