const API_BASE = 'http://localhost:3000/api';

async function apiRequest(url, options = {}) {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });

  const data = await response.json();
  return { status: response.status, data };
}

async function testAPIValidation() {
  try {
    console.log('🚀 开始测试审核API验证功能...');

    // 1. 登录获取token
    console.log('📝 登录审核员账户...');
    const loginResponse = await apiRequest(`${API_BASE}/auth/login`, {
      method: 'POST',
      body: JSON.stringify({
        username: 'reviewer',
        password: '123456'
      })
    });

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功，获取到token');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 获取待审核合同列表
    console.log('📋 获取待审核合同列表...');
    const contractsResponse = await apiRequest(`${API_BASE}/contracts?status=pending`, {
      method: 'GET',
      headers
    });

    console.log('合同列表响应:', JSON.stringify(contractsResponse.data, null, 2));
    const pendingContracts = contractsResponse.data.data?.contracts || contractsResponse.data.data || [];

    if (pendingContracts.length === 0) {
      console.log('❌ 没有待审核的合同');
      return;
    }

    const testContractId = pendingContracts[0].id;
    console.log(`✅ 找到测试合同ID: ${testContractId}`);

    // 测试场景1：审核通过时不填写说明
    console.log('\n🧪 测试场景1：审核通过时不填写说明');
    try {
      const response1 = await apiRequest(`${API_BASE}/contracts/${testContractId}/review`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          result: 'approved',
          comment: ''
        })
      });

      console.log('✅ 审核通过（无说明）API调用成功');
      console.log('响应状态:', response1.status);
    } catch (error) {
      console.log('❌ 审核通过（无说明）API调用失败');
      console.log('错误信息:', error.message);
    }

    // 如果第一个合同已被审核，使用下一个
    let nextContractId = testContractId;
    if (pendingContracts.length > 1) {
      nextContractId = pendingContracts[1].id;
    }

    // 测试场景2：审核通过时填写少于10字符说明
    console.log('\n🧪 测试场景2：审核通过时填写少于10字符说明');
    try {
      const response2 = await apiRequest(`${API_BASE}/contracts/${nextContractId}/review`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          result: 'approved',
          comment: '同意'
        })
      });

      console.log('✅ 审核通过（少于10字符说明）API调用成功');
      console.log('响应状态:', response2.status);
    } catch (error) {
      console.log('❌ 审核通过（少于10字符说明）API调用失败');
      console.log('错误信息:', error.message);
    }

    // 使用下一个合同测试驳回场景
    let rejectContractId = testContractId;
    if (pendingContracts.length > 2) {
      rejectContractId = pendingContracts[2].id;
    }

    // 测试场景3：审核驳回时不填写说明
    console.log('\n🧪 测试场景3：审核驳回时不填写说明');
    try {
      const response3 = await apiRequest(`${API_BASE}/contracts/${rejectContractId}/review`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          result: 'rejected',
          comment: ''
        })
      });

      if (response3.status >= 400) {
        console.log('✅ 审核驳回（无说明）API调用正确失败');
        console.log('错误信息:', response3.data.message);
      } else {
        console.log('❌ 审核驳回（无说明）API调用成功（应该失败）');
        console.log('响应状态:', response3.status);
      }
    } catch (error) {
      console.log('✅ 审核驳回（无说明）API调用正确失败');
      console.log('错误信息:', error.message);
    }

    // 测试场景4：审核驳回时填写少于10字符说明
    console.log('\n🧪 测试场景4：审核驳回时填写少于10字符说明');
    try {
      const response4 = await apiRequest(`${API_BASE}/contracts/${rejectContractId}/review`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          result: 'rejected',
          comment: '不行'
        })
      });

      if (response4.status >= 400) {
        console.log('✅ 审核驳回（少于10字符说明）API调用正确失败');
        console.log('错误信息:', response4.data.message);
      } else {
        console.log('❌ 审核驳回（少于10字符说明）API调用成功（应该失败）');
        console.log('响应状态:', response4.status);
      }
    } catch (error) {
      console.log('✅ 审核驳回（少于10字符说明）API调用正确失败');
      console.log('错误信息:', error.message);
    }

    // 测试场景5：审核驳回时填写10字符以上说明
    console.log('\n🧪 测试场景5：审核驳回时填写10字符以上说明');
    try {
      const response5 = await apiRequest(`${API_BASE}/contracts/${rejectContractId}/review`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          result: 'rejected',
          comment: '合同内容不符合要求，请重新修改后提交'
        })
      });

      if (response5.status < 400) {
        console.log('✅ 审核驳回（10字符以上说明）API调用成功');
        console.log('响应状态:', response5.status);
      } else {
        console.log('❌ 审核驳回（10字符以上说明）API调用失败');
        console.log('错误信息:', response5.data.message);
      }
    } catch (error) {
      console.log('❌ 审核驳回（10字符以上说明）API调用失败');
      console.log('错误信息:', error.message);
    }

    console.log('\n🎉 所有API测试场景完成！');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 运行测试
testAPIValidation().catch(console.error);
