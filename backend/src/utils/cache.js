/**
 * 内存缓存管理器
 * 提供高性能的内存缓存功能，支持TTL、LRU淘汰策略等
 */

class LRUCache {
  constructor(maxSize = 100) {
    this.maxSize = maxSize;
    this.cache = new Map();
    this.accessOrder = [];
  }

  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序
      this.moveToFront(key);
      return this.cache.get(key);
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      // 更新现有值
      this.cache.set(key, value);
      this.moveToFront(key);
    } else {
      // 添加新值
      if (this.cache.size >= this.maxSize) {
        // 淘汰最少使用的项
        const lruKey = this.accessOrder.pop();
        this.cache.delete(lruKey);
      }
      this.cache.set(key, value);
      this.accessOrder.unshift(key);
    }
  }

  delete(key) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
      this.accessOrder = this.accessOrder.filter(k => k !== key);
      return true;
    }
    return false;
  }

  clear() {
    this.cache.clear();
    this.accessOrder = [];
  }

  size() {
    return this.cache.size;
  }

  moveToFront(key) {
    this.accessOrder = this.accessOrder.filter(k => k !== key);
    this.accessOrder.unshift(key);
  }
}

class CacheManager {
  constructor(options = {}) {
    this.defaultTTL = options.defaultTTL || 300000; // 5分钟
    this.checkInterval = options.checkInterval || 60000; // 1分钟
    this.maxSize = options.maxSize || 1000;

    // 不同类型的缓存存储
    this.caches = {
      users: new LRUCache(100),
      contracts: new LRUCache(500),
      statistics: new LRUCache(50),
      search: new LRUCache(200),
      files: new LRUCache(300)
    };

    // TTL管理
    this.ttlMap = new Map();

    // 缓存统计
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };

    // 记录启动时间
    this.startTime = Date.now();

    // 启动清理定时器
    this.startCleanupTimer();
  }

  /**
   * 生成缓存键
   */
  generateKey(type, identifier, params = {}) {
    const paramString = Object.keys(params).length > 0
      ? ':' + Object.entries(params)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([k, v]) => `${k}=${v}`)
        .join('&')
      : '';

    return `${type}:${identifier}${paramString}`;
  }

  /**
   * 获取缓存值
   */
  get(type, identifier, params = {}) {
    const key = this.generateKey(type, identifier, params);

    // 检查TTL
    if (this.isExpired(key)) {
      this.delete(type, identifier, params);
      this.stats.misses++;
      return null;
    }

    const cache = this.caches[type];
    if (!cache) {
      this.stats.misses++;
      return null;
    }

    const value = cache.get(key);
    if (value) {
      this.stats.hits++;
      return value;
    } else {
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 设置缓存值
   */
  set(type, identifier, value, ttl = null, params = {}) {
    const key = this.generateKey(type, identifier, params);
    const cache = this.caches[type];

    if (!cache) {
      console.warn(`Cache type '${type}' not found`);
      return false;
    }

    // 设置值
    cache.set(key, value);

    // 设置TTL
    const expirationTime = Date.now() + (ttl || this.defaultTTL);
    this.ttlMap.set(key, expirationTime);

    this.stats.sets++;
    return true;
  }

  /**
   * 删除缓存值
   */
  delete(type, identifier, params = {}) {
    const key = this.generateKey(type, identifier, params);
    const cache = this.caches[type];

    if (!cache) {
      return false;
    }

    const deleted = cache.delete(key);
    this.ttlMap.delete(key);

    if (deleted) {
      this.stats.deletes++;
    }

    return deleted;
  }

  /**
   * 清空指定类型的缓存
   */
  clearType(type) {
    const cache = this.caches[type];
    if (!cache) {
      return false;
    }

    // 清理TTL映射
    for (const [key] of this.ttlMap.entries()) {
      if (key.startsWith(`${type}:`)) {
        this.ttlMap.delete(key);
      }
    }

    cache.clear();
    return true;
  }

  /**
   * 清空所有缓存
   */
  clearAll() {
    Object.values(this.caches).forEach(cache => cache.clear());
    this.ttlMap.clear();
    this.resetStats();
  }

  /**
   * 检查缓存是否过期
   */
  isExpired(key) {
    const expirationTime = this.ttlMap.get(key);
    if (!expirationTime) {
      return false;
    }

    return Date.now() > expirationTime;
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests * 100).toFixed(2) : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      totalRequests,
      cacheSize: Object.entries(this.caches).reduce((total, [type, cache]) => {
        return { ...total, [type]: cache.size() };
      }, {}),
      totalCacheSize: Object.values(this.caches).reduce((total, cache) => total + cache.size(), 0),
      cacheTypes: Object.keys(this.caches).map(type => ({
        type,
        size: this.caches[type].size(),
        maxSize: this.caches[type].maxSize,
        utilization: `${((this.caches[type].size() / this.caches[type].maxSize) * 100).toFixed(2)}%`
      })),
      ttlEntries: this.ttlMap.size,
      performance: this.getPerformanceMetrics()
    };
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    const now = Date.now();
    const uptime = now - (this.startTime || now);

    return {
      uptime: Math.floor(uptime / 1000), // 秒
      requestsPerSecond: this.stats.hits + this.stats.misses > 0
        ? ((this.stats.hits + this.stats.misses) / (uptime / 1000)).toFixed(2)
        : 0,
      memoryUsage: process.memoryUsage(),
      cacheEfficiency: {
        hitRate: this.stats.hits + this.stats.misses > 0
          ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
          : 0,
        missRate: this.stats.hits + this.stats.misses > 0
          ? (this.stats.misses / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
          : 0
      }
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.checkInterval);
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, expirationTime] of this.ttlMap.entries()) {
      if (now > expirationTime) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      // 从TTL映射中删除
      this.ttlMap.delete(key);

      // 从相应的缓存中删除
      const [type] = key.split(':');
      const cache = this.caches[type];
      if (cache) {
        cache.delete(key);
        this.stats.evictions++;
      }
    });

    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    this.stopCleanupTimer();
    this.clearAll();
  }
}

// 缓存装饰器
const cacheDecorator = (type, keyGenerator, ttl = null) => {
  return function (target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args) {
      const key = typeof keyGenerator === 'function'
        ? keyGenerator(...args)
        : keyGenerator;

      // 尝试从缓存获取
      const cached = cacheManager.get(type, key);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args);

      // 缓存结果
      if (result !== null && result !== undefined) {
        cacheManager.set(type, key, result, ttl);
      }

      return result;
    };

    return descriptor;
  };
};

// 创建全局缓存管理器实例
const cacheManager = new CacheManager({
  defaultTTL: 300000, // 5分钟
  checkInterval: 60000, // 1分钟检查一次
  maxSize: 2000
});

// 缓存键生成工具
const CacheKeys = {
  user: (id) => `user_${id}`,
  userList: (filters = {}) => {
    const filterStr = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join('&');
    return `user_list_${filterStr}`;
  },

  contract: (id) => `contract_${id}`,
  contractList: (filters = {}, page = 1, limit = 10) => {
    const filterStr = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join('&');
    return `contract_list_${filterStr}_p${page}_l${limit}`;
  },

  statistics: (type, period = 'month') => `stats_${type}_${period}`,

  search: (query, type = 'all') => `search_${type}_${Buffer.from(query).toString('base64')}`,

  file: (filename) => `file_${filename}`
};

// 缓存辅助函数
const CacheHelpers = {
  /**
   * 缓存用户数据
   */
  async cacheUser(userId, userData, ttl = 300000) {
    return cacheManager.set('users', CacheKeys.user(userId), userData, ttl);
  },

  /**
   * 获取缓存的用户数据
   */
  async getCachedUser(userId) {
    return cacheManager.get('users', CacheKeys.user(userId));
  },

  /**
   * 缓存合同数据
   */
  async cacheContract(contractId, contractData, ttl = 300000) {
    return cacheManager.set('contracts', CacheKeys.contract(contractId), contractData, ttl);
  },

  /**
   * 获取缓存的合同数据
   */
  async getCachedContract(contractId) {
    return cacheManager.get('contracts', CacheKeys.contract(contractId));
  },

  /**
   * 缓存统计数据
   */
  async cacheStatistics(type, period, data, ttl = 600000) {
    return cacheManager.set('statistics', CacheKeys.statistics(type, period), data, ttl);
  },

  /**
   * 获取缓存的统计数据
   */
  async getCachedStatistics(type, period) {
    return cacheManager.get('statistics', CacheKeys.statistics(type, period));
  },

  /**
   * 缓存搜索结果
   */
  async cacheSearchResult(query, type, results, ttl = 180000) {
    return cacheManager.set('search', CacheKeys.search(query, type), results, ttl);
  },

  /**
   * 获取缓存的搜索结果
   */
  async getCachedSearchResult(query, type) {
    return cacheManager.get('search', CacheKeys.search(query, type));
  },

  /**
   * 清理相关缓存
   */
  async invalidateUserCache(userId) {
    cacheManager.delete('users', CacheKeys.user(userId));
    cacheManager.clearType('statistics'); // 用户变化可能影响统计
  },

  async invalidateContractCache(contractId) {
    cacheManager.delete('contracts', CacheKeys.contract(contractId));
    cacheManager.clearType('statistics'); // 合同变化可能影响统计
    cacheManager.clearType('search'); // 合同变化可能影响搜索结果
  },

  async invalidateStatisticsCache() {
    cacheManager.clearType('statistics');
  },

  async invalidateSearchCache() {
    cacheManager.clearType('search');
  }
};

module.exports = {
  CacheManager,
  cacheManager,
  cacheDecorator,
  CacheKeys,
  CacheHelpers
};