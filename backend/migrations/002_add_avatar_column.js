#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 添加用户头像字段
 * 
 * 此迁移将：
 * 1. 在 users 表中添加 avatar 字段
 * 2. 创建头像上传目录
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行头像字段迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 主迁移函数
async function runMigration() {
  try {
    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  头像字段迁移已完成，跳过执行');
      return;
    }

    // 1. 添加avatar字段到users表
    await addAvatarColumn();

    // 2. 创建头像上传目录
    await createAvatarDirectory();

    // 3. 记录迁移完成
    await recordMigration();

    console.log('🎉 头像字段迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    const migration = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'add_avatar_column'
    `);

    return { completed: !!migration };
  } catch (error) {
    return { completed: false };
  }
}

// 添加avatar字段
async function addAvatarColumn() {
  try {
    await runQuery('ALTER TABLE users ADD COLUMN avatar TEXT');
    console.log('✅ 添加avatar字段到users表');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('ℹ️  avatar字段已存在');
    } else {
      throw error;
    }
  }
}

// 创建头像上传目录
async function createAvatarDirectory() {
  const avatarDir = path.join(__dirname, '../uploads/avatars');

  if (!fs.existsSync(avatarDir)) {
    fs.mkdirSync(avatarDir, { recursive: true });
    console.log('✅ 创建头像上传目录');
  } else {
    console.log('ℹ️  头像上传目录已存在');
  }

  // 创建.gitkeep文件保持目录
  const gitkeepPath = path.join(avatarDir, '.gitkeep');
  if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, '');
    console.log('✅ 创建.gitkeep文件');
  }
}

// 记录迁移完成
async function recordMigration() {
  await runQuery(`
    INSERT OR IGNORE INTO migrations (migration_name, version)
    VALUES ('add_avatar_column', '1.0.0')
  `);
  console.log('✅ 记录迁移完成');
}

// 执行迁移
runMigration();
