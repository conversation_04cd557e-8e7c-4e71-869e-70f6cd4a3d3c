#!/usr/bin/env node

/**
 * Vite 健康检查脚本
 * 检查 Vite 开发服务器状态和缓存健康度
 */

import { exec } from "child_process";
import { promisify } from "util";
import { existsSync, statSync } from "fs";
import { join } from "path";

const execAsync = promisify(exec);

class ViteHealthChecker {
  constructor() {
    this.baseDir = process.cwd();
    this.cacheDir = join(this.baseDir, "node_modules/.vite");
    this.distDir = join(this.baseDir, "dist");
  }

  // 检查缓存目录状态
  checkCacheHealth() {
    console.log("🔍 检查 Vite 缓存状态...");

    if (!existsSync(this.cacheDir)) {
      console.log("✅ 缓存目录不存在，将在首次运行时创建");
      return { status: "clean", message: "缓存目录干净" };
    }

    const stats = statSync(this.cacheDir);
    const cacheAge = Date.now() - stats.mtime.getTime();
    const ageInHours = cacheAge / (1000 * 60 * 60);

    if (ageInHours > 24) {
      console.log("⚠️  缓存目录较旧，建议清理");
      return {
        status: "old",
        message: `缓存已存在 ${ageInHours.toFixed(1)} 小时`,
      };
    }

    console.log("✅ 缓存目录状态正常");
    return { status: "healthy", message: "缓存状态良好" };
  }

  // 检查依赖预构建状态
  async checkDependencies() {
    console.log("📦 检查依赖预构建状态...");

    try {
      const { stdout } = await execAsync("npm list --depth=0 --json");
      const packageInfo = JSON.parse(stdout);
      const dependencies = Object.keys(packageInfo.dependencies || {});

      console.log(`✅ 发现 ${dependencies.length} 个直接依赖`);

      // 检查关键依赖
      const criticalDeps = ["vue", "vue-router", "element-plus", "axios"];
      const missingDeps = criticalDeps.filter(
        (dep) => !dependencies.includes(dep),
      );

      if (missingDeps.length > 0) {
        console.log("⚠️  缺少关键依赖:", missingDeps.join(", "));
        return { status: "incomplete", missing: missingDeps };
      }

      console.log("✅ 所有关键依赖都已安装");
      return { status: "complete", dependencies };
    } catch (error) {
      console.log("❌ 依赖检查失败:", error.message);
      return { status: "error", error: error.message };
    }
  }

  // 检查 Vite 配置
  checkViteConfig() {
    console.log("⚙️  检查 Vite 配置...");

    const configPath = join(this.baseDir, "vite.config.js");
    if (!existsSync(configPath)) {
      console.log("❌ vite.config.js 不存在");
      return { status: "missing", message: "配置文件缺失" };
    }

    console.log("✅ Vite 配置文件存在");
    return { status: "exists", message: "配置文件正常" };
  }

  // 运行完整健康检查
  async runHealthCheck() {
    console.log("🏥 开始 Vite 健康检查...\n");

    const results = {
      cache: this.checkCacheHealth(),
      dependencies: await this.checkDependencies(),
      config: this.checkViteConfig(),
      timestamp: new Date().toISOString(),
    };

    console.log("\n📊 健康检查报告:");
    console.log("================");
    console.log(`缓存状态: ${results.cache.status} - ${results.cache.message}`);
    console.log(`依赖状态: ${results.dependencies.status}`);
    console.log(
      `配置状态: ${results.config.status} - ${results.config.message}`,
    );

    // 给出建议
    console.log("\n💡 建议操作:");
    if (results.cache.status === "old") {
      console.log("- 运行 npm run clean:cache 清理缓存");
    }
    if (results.dependencies.status === "incomplete") {
      console.log("- 运行 npm install 安装缺失依赖");
    }
    if (
      results.cache.status === "clean" &&
      results.dependencies.status === "complete"
    ) {
      console.log("- 系统状态良好，可以正常启动开发服务器");
    }

    return results;
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new ViteHealthChecker();
  checker.runHealthCheck().catch(console.error);
}

export default ViteHealthChecker;
