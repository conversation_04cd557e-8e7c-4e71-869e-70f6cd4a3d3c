/**
 * 合同状态流转管理
 * 定义合同状态、流转规则和相关工具函数
 */

// 合同状态常量
export const CONTRACT_STATUS = {
  PENDING: "pending", // 待县局审核
  PENDING_CITY_REVIEW: "pending_city_review", // 待市局审核
  APPROVED: "approved", // 已通过
  REJECTED: "rejected", // 已拒绝
};

// 状态显示名称
export const STATUS_LABELS = {
  [CONTRACT_STATUS.PENDING]: "待县局审核",
  [CONTRACT_STATUS.PENDING_CITY_REVIEW]: "待市局审核",
  [CONTRACT_STATUS.APPROVED]: "已通过",
  [CONTRACT_STATUS.REJECTED]: "已拒绝",
};

// 状态颜色映射
export const STATUS_COLORS = {
  [CONTRACT_STATUS.PENDING]: "warning",
  [CONTRACT_STATUS.PENDING_CITY_REVIEW]: "warning",
  [CONTRACT_STATUS.APPROVED]: "success",
  [CONTRACT_STATUS.REJECTED]: "danger",
};

// 状态图标映射
export const STATUS_ICONS = {
  [CONTRACT_STATUS.PENDING]: "Timer",
  [CONTRACT_STATUS.PENDING_CITY_REVIEW]: "Timer",
  [CONTRACT_STATUS.APPROVED]: "CircleCheckFilled",
  [CONTRACT_STATUS.REJECTED]: "CircleCloseFilled",
};

// 状态流转规则 - 与后端保持严格一致
export const STATUS_TRANSITIONS = {
  [CONTRACT_STATUS.PENDING]: [
    CONTRACT_STATUS.PENDING_CITY_REVIEW,
    CONTRACT_STATUS.APPROVED,
    CONTRACT_STATUS.REJECTED,
  ], // 待县局审核可流转到市局审核、直接通过或拒绝
  [CONTRACT_STATUS.PENDING_CITY_REVIEW]: [
    CONTRACT_STATUS.APPROVED,
    CONTRACT_STATUS.REJECTED,
  ], // 待市局审核可通过或拒绝
  [CONTRACT_STATUS.APPROVED]: [], // 终态，不可变更
  [CONTRACT_STATUS.REJECTED]: [
    CONTRACT_STATUS.PENDING, // 重新提交后回到待县局审核状态
  ],
};

// 用户角色权限
export const USER_ROLES = {
  EMPLOYEE: "employee",
  REVIEWER: "reviewer",
  COUNTY_REVIEWER: "county_reviewer",
  CITY_REVIEWER: "city_reviewer",
  ADMIN: "admin",
};

// 角色权限映射
export const ROLE_PERMISSIONS = {
  [USER_ROLES.EMPLOYEE]: {
    canSubmit: true,
    canModify: true,
    canView: true,
    canDelete: true,
    canReview: false,
    canManage: false,
  },
  [USER_ROLES.REVIEWER]: {
    canSubmit: false,
    canModify: false,
    canView: true,
    canDelete: false,
    canReview: true,
    canManage: false,
  },
  [USER_ROLES.COUNTY_REVIEWER]: {
    canSubmit: false,
    canModify: false,
    canView: true,
    canDelete: false,
    canReview: true,
    canManage: false,
  },
  [USER_ROLES.CITY_REVIEWER]: {
    canSubmit: false,
    canModify: false,
    canView: true,
    canDelete: false,
    canReview: true,
    canManage: false,
  },
  [USER_ROLES.ADMIN]: {
    canSubmit: true,
    canModify: true,
    canView: true,
    canDelete: true,
    canReview: true,
    canManage: true,
  },
};

/**
 * 状态工具类
 */
export class ContractStatusManager {
  /**
   * 格式化状态显示
   * @param {string} status - 状态值
   * @returns {string} 显示名称
   */
  static formatStatus(status) {
    return STATUS_LABELS[status] || status;
  }

  /**
   * 获取状态颜色
   * @param {string} status - 状态值
   * @returns {string} 颜色类型
   */
  static getStatusColor(status) {
    return STATUS_COLORS[status] || "info";
  }

  /**
   * 获取状态图标
   * @param {string} status - 状态值
   * @returns {string} 图标名称
   */
  static getStatusIcon(status) {
    return STATUS_ICONS[status] || "Document";
  }

  /**
   * 检查状态是否可以流转
   * @param {string} fromStatus - 当前状态
   * @param {string} toStatus - 目标状态
   * @returns {boolean} 是否可以流转
   */
  static canTransition(fromStatus, toStatus) {
    const allowedTransitions = STATUS_TRANSITIONS[fromStatus] || [];
    return allowedTransitions.includes(toStatus);
  }

  /**
   * 验证状态转换并返回错误信息
   * @param {string} fromStatus - 当前状态
   * @param {string} toStatus - 目标状态
   * @returns {Object} { valid: boolean, error?: string }
   */
  static validateTransition(fromStatus, toStatus) {
    if (!fromStatus || !toStatus) {
      return { valid: false, error: "状态参数不能为空" };
    }

    if (!Object.values(CONTRACT_STATUS).includes(fromStatus)) {
      return { valid: false, error: `无效的当前状态: ${fromStatus}` };
    }

    if (!Object.values(CONTRACT_STATUS).includes(toStatus)) {
      return { valid: false, error: `无效的目标状态: ${toStatus}` };
    }

    if (!this.canTransition(fromStatus, toStatus)) {
      return {
        valid: false,
        error: `不允许从 ${STATUS_LABELS[fromStatus]} 转换到 ${STATUS_LABELS[toStatus]}`,
      };
    }

    return { valid: true };
  }

  /**
   * 获取可流转的状态列表
   * @param {string} currentStatus - 当前状态
   * @returns {Array} 可流转的状态列表
   */
  static getAvailableTransitions(currentStatus) {
    return STATUS_TRANSITIONS[currentStatus] || [];
  }

  /**
   * 检查用户是否可以修改合同
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以修改
   */
  static canModifyContract(contract, user) {
    if (!contract || !user) return false;

    // 管理员不能修改合同内容（根据PRD要求）
    if (user.role === USER_ROLES.ADMIN) return false;

    const permissions = ROLE_PERMISSIONS[user.role];
    if (!permissions?.canModify) return false;

    // 员工可以修改自己的待审核或被拒绝的合同
    if (user.role === USER_ROLES.EMPLOYEE) {
      return (
        contract.submitter_id === user.id &&
        [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.REJECTED].includes(
          contract.status,
        )
      );
    }

    return false;
  }

  /**
   * 检查用户是否可以审核合同
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以审核
   */
  static canReviewContract(contract, user) {
    if (!contract || !user) return false;

    // 只有待审核状态的合同可以审核（支持县局和市局审核）
    const reviewableStatuses = [
      CONTRACT_STATUS.PENDING,
      CONTRACT_STATUS.PENDING_CITY_REVIEW,
    ];
    if (!reviewableStatuses.includes(contract.status)) {
      return false;
    }

    const permissions = ROLE_PERMISSIONS[user.role];
    if (!permissions?.canReview) return false;

    // 不能审核自己提交的合同
    if (contract.submitter_id === user.id) return false;

    // 管理员可以审核任何合同（除了自己提交的）
    if (user.role === USER_ROLES.ADMIN) return true;

    // 审核员只能审核分配给自己的合同
    if (
      [
        USER_ROLES.REVIEWER,
        USER_ROLES.COUNTY_REVIEWER,
        USER_ROLES.CITY_REVIEWER,
      ].includes(user.role) &&
      contract.reviewer_id === user.id
    ) {
      // 额外检查：市局审核员只能审核待市局审核的合同
      if (
        user.role === USER_ROLES.CITY_REVIEWER &&
        contract.status !== CONTRACT_STATUS.PENDING_CITY_REVIEW
      ) {
        return false;
      }
      // 县局审核员只能审核待审核的合同
      if (
        (user.role === USER_ROLES.COUNTY_REVIEWER ||
          user.role === USER_ROLES.REVIEWER) &&
        contract.status !== CONTRACT_STATUS.PENDING
      ) {
        return false;
      }
      return true;
    }

    return false;
  }

  /**
   * 检查用户是否可以删除合同
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以删除
   */
  static canDeleteContract(contract, user) {
    if (!contract || !user) return false;

    const permissions = ROLE_PERMISSIONS[user.role];
    if (!permissions?.canDelete) return false;

    // 管理员可以删除任何合同
    if (user.role === USER_ROLES.ADMIN) return true;

    // 员工只能删除自己的待审核或被拒绝的合同
    if (user.role === USER_ROLES.EMPLOYEE) {
      return (
        contract.submitter_id === user.id &&
        [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.REJECTED].includes(
          contract.status,
        )
      );
    }

    return false;
  }

  /**
   * 检查用户是否可以查看合同
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以查看
   */
  static canViewContract(contract, user) {
    if (!contract || !user) return false;

    const permissions = ROLE_PERMISSIONS[user.role];
    if (!permissions?.canView) return false;

    // 管理员可以查看任何合同
    if (user.role === USER_ROLES.ADMIN) return true;

    // 员工可以查看自己的合同
    if (user.role === USER_ROLES.EMPLOYEE) {
      return contract.submitter_id === user.id;
    }

    // 审核员可以查看分配给自己的合同
    if (
      [
        USER_ROLES.REVIEWER,
        USER_ROLES.COUNTY_REVIEWER,
        USER_ROLES.CITY_REVIEWER,
      ].includes(user.role)
    ) {
      return contract.reviewer_id === user.id;
    }

    return false;
  }

  /**
   * 获取状态描述
   * @param {string} status - 状态值
   * @returns {string} 状态描述
   */
  static getStatusDescription(status) {
    const descriptions = {
      [CONTRACT_STATUS.PENDING]: "合同已提交，等待审核人员审核",
      [CONTRACT_STATUS.APPROVED]: "合同审核通过，流程完成",
      [CONTRACT_STATUS.REJECTED]: "合同审核不通过，需要修改后重新提交",
    };
    return descriptions[status] || "未知状态";
  }

  /**
   * 获取下一步操作提示
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {string} 操作提示
   */
  static getNextActionHint(contract, user) {
    if (!contract || !user) return "";

    const { status } = contract;
    const { role } = user;

    if (role === USER_ROLES.EMPLOYEE) {
      switch (status) {
        case CONTRACT_STATUS.PENDING:
          return "等待审核人员审核";
        case CONTRACT_STATUS.APPROVED:
          return "审核已通过，流程完成";
        case CONTRACT_STATUS.REJECTED:
          return "审核不通过，您可以修改后重新提交";
        default:
          return "";
      }
    }

    if (role === USER_ROLES.REVIEWER || role === USER_ROLES.ADMIN) {
      switch (status) {
        case CONTRACT_STATUS.PENDING:
          return "您可以开始审核此合同";
        case CONTRACT_STATUS.APPROVED:
        case CONTRACT_STATUS.REJECTED:
          return "审核已完成";
        default:
          return "";
      }
    }

    return "";
  }
}

// 导出默认实例
export default ContractStatusManager;
