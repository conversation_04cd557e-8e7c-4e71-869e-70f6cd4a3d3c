<template>
  <div class="avatar-uploader">
    <!-- 头像显示区域 -->
    <div class="avatar-display" @click="triggerFileSelect">
      <div class="avatar-container">
        <img
          v-if="currentAvatar"
          :src="currentAvatar"
          :alt="altText"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          <el-icon :size="40">
            <User />
          </el-icon>
          <span class="placeholder-text">{{ placeholderText }}</span>
        </div>

        <!-- 悬停遮罩 -->
        <div class="avatar-overlay">
          <el-icon :size="24">
            <Camera />
          </el-icon>
          <span class="overlay-text">{{ overlayText }}</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 裁剪对话框 -->
    <el-dialog
      v-model="cropDialogVisible"
      :title="dialogTitle"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="avatar-crop-dialog"
      @close="handleDialogClose"
    >
      <div class="crop-container">
        <!-- 裁剪区域 -->
        <div class="crop-area">
          <div class="crop-wrapper">
            <canvas
              ref="cropCanvasRef"
              class="crop-canvas"
              @mousedown="startCrop"
              @mousemove="updateCrop"
              @mouseup="endCrop"
              @touchstart="startCrop"
              @touchmove="updateCrop"
              @touchend="endCrop"
            ></canvas>

            <!-- 裁剪框调整控制点 -->
            <div
              v-if="cropState.cropWidth > 0"
              class="crop-resize-handles"
              :style="{
                left: cropState.cropX + 'px',
                top: cropState.cropY + 'px',
                width: cropState.cropWidth + 'px',
                height: cropState.cropHeight + 'px',
              }"
            >
              <!-- 四个角的调整点 -->
              <div
                class="resize-handle corner-tl"
                @mousedown="startResize('tl', $event)"
              ></div>
              <div
                class="resize-handle corner-tr"
                @mousedown="startResize('tr', $event)"
              ></div>
              <div
                class="resize-handle corner-bl"
                @mousedown="startResize('bl', $event)"
              ></div>
              <div
                class="resize-handle corner-br"
                @mousedown="startResize('br', $event)"
              ></div>

              <!-- 四边的调整点 -->
              <div
                class="resize-handle edge-t"
                @mousedown="startResize('t', $event)"
              ></div>
              <div
                class="resize-handle edge-r"
                @mousedown="startResize('r', $event)"
              ></div>
              <div
                class="resize-handle edge-b"
                @mousedown="startResize('b', $event)"
              ></div>
              <div
                class="resize-handle edge-l"
                @mousedown="startResize('l', $event)"
              ></div>
            </div>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area">
          <div class="preview-title">
            <el-icon><View /></el-icon>
            <span>预览效果</span>
          </div>

          <!-- 圆形预览 -->
          <div class="preview-item">
            <div class="preview-label">头像预览</div>
            <div ref="previewCircleRef" class="preview-circle"></div>
            <div class="preview-size">{{ avatarSize }}x{{ avatarSize }}px</div>
          </div>

          <!-- 方形预览 -->
          <div class="preview-item">
            <div class="preview-label">方形预览</div>
            <div ref="previewSquareRef" class="preview-square"></div>
            <div class="preview-size">{{ avatarSize }}x{{ avatarSize }}px</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <div class="crop-controls">
            <el-button-group>
              <el-button :icon="RefreshLeft" @click="rotateLeft">
                左转
              </el-button>
              <el-button :icon="RefreshRight" @click="rotateRight">
                右转
              </el-button>
              <el-button :icon="Switch" @click="flipHorizontal">
                水平翻转
              </el-button>
              <el-button :icon="SwitchButton" @click="flipVertical">
                垂直翻转
              </el-button>
              <el-button :icon="Refresh" @click="resetCrop"> 重置 </el-button>
            </el-button-group>
          </div>

          <div class="action-buttons">
            <el-button @click="handleCancel"> 取消 </el-button>
            <el-button :icon="FolderOpened" @click="reSelectFile">
              重新选择
            </el-button>
            <el-button
              type="primary"
              :loading="processing"
              :icon="Check"
              @click="handleConfirm"
            >
              {{ processing ? "处理中..." : "确认裁剪" }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import {
  User,
  Camera,
  View,
  RefreshLeft,
  RefreshRight,
  Switch,
  SwitchButton,
  Refresh,
  FolderOpened,
  Check,
} from "@element-plus/icons-vue";

// Props定义
const props = defineProps({
  // 当前头像URL
  modelValue: {
    type: String,
    default: "",
  },
  // 头像尺寸
  avatarSize: {
    type: Number,
    default: 100,
  },
  // 裁剪输出尺寸
  outputSize: {
    type: Number,
    default: 200,
  },
  // 输出质量 (0-1)
  outputQuality: {
    type: Number,
    default: 0.9,
  },
  // 输出格式
  outputFormat: {
    type: String,
    default: "image/jpeg",
  },
  // 最大文件大小 (字节)
  maxFileSize: {
    type: Number,
    default: 5 * 1024 * 1024, // 5MB
  },
  // 占位符文本
  placeholderText: {
    type: String,
    default: "点击上传头像",
  },
  // 悬停文本
  overlayText: {
    type: String,
    default: "更换头像",
  },
  // 对话框标题
  dialogTitle: {
    type: String,
    default: "裁剪头像",
  },
  // alt文本
  altText: {
    type: String,
    default: "用户头像",
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
});

// Emits定义
const emit = defineEmits([
  "update:modelValue",
  "crop-complete",
  "crop-cancel",
  "file-select",
]);

// 响应式数据
const fileInputRef = ref();
const cropCanvasRef = ref();
const previewCircleRef = ref();
const previewSquareRef = ref();

const cropDialogVisible = ref(false);
const originalImageUrl = ref("");
const currentAvatar = ref(props.modelValue);
const processing = ref(false);

// 裁剪相关状态
const cropState = ref({
  isDragging: false,
  isResizing: false,
  resizeType: "",
  startX: 0,
  startY: 0,
  cropX: 0,
  cropY: 0,
  cropWidth: 200,
  cropHeight: 200,
  scale: 1,
  rotation: 0,
});

let originalImage = null;
let canvasContext = null;

// 文件选择处理
const triggerFileSelect = () => {
  console.log("🔥 triggerFileSelect called!");
  console.log("props.disabled:", props.disabled);
  console.log("fileInputRef.value:", fileInputRef.value);

  if (props.disabled) {
    console.log("❌ Component is disabled");
    return;
  }

  if (fileInputRef.value) {
    console.log("✅ Clicking file input...");
    fileInputRef.value.click();
  } else {
    console.error("❌ File input ref is null!");
  }
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith("image/")) {
    ElMessage.error("请选择图片文件");
    return;
  }

  // 验证文件大小
  if (file.size > props.maxFileSize) {
    const maxSizeMB = (props.maxFileSize / (1024 * 1024)).toFixed(1);
    ElMessage.error(`文件大小不能超过 ${maxSizeMB}MB`);
    return;
  }

  // 读取文件并显示裁剪对话框
  const reader = new FileReader();
  reader.onload = (e) => {
    originalImageUrl.value = e.target.result;
    cropDialogVisible.value = true;

    nextTick(() => {
      initCropper();
    });
  };
  reader.onerror = (e) => {
    console.error("文件读取失败:", e);
    ElMessage.error("文件读取失败");
  };
  reader.readAsDataURL(file);

  // 触发文件选择事件
  emit("file-select", file);

  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 初始化Canvas裁剪器
const initCropper = () => {
  if (!cropCanvasRef.value) return;

  const canvas = cropCanvasRef.value;
  canvasContext = canvas.getContext("2d");

  // 创建图片对象
  originalImage = new Image();
  originalImage.onload = () => {
    // 设置canvas尺寸
    const maxWidth = 400;
    const maxHeight = 300;
    let { width, height } = originalImage;

    // 计算缩放比例
    const scale = Math.min(maxWidth / width, maxHeight / height);
    width *= scale;
    height *= scale;

    canvas.width = width;
    canvas.height = height;

    // 初始化裁剪区域（居中）
    const cropSize = Math.min(width, height) * 0.8;
    cropState.value.cropX = (width - cropSize) / 2;
    cropState.value.cropY = (height - cropSize) / 2;
    cropState.value.cropWidth = cropSize;
    cropState.value.cropHeight = cropSize;
    cropState.value.scale = scale;

    // 绘制图片和裁剪框
    drawCanvas();
    updatePreview();
  };
  originalImage.src = originalImageUrl.value;
};

// 绘制Canvas
const drawCanvas = () => {
  if (!canvasContext || !originalImage) return;

  const canvas = cropCanvasRef.value;
  const { width, height } = canvas;

  // 清空画布
  canvasContext.clearRect(0, 0, width, height);

  // 绘制原图
  canvasContext.save();
  canvasContext.translate(width / 2, height / 2);
  canvasContext.rotate((cropState.value.rotation * Math.PI) / 180);
  canvasContext.scale(cropState.value.scale, cropState.value.scale);
  canvasContext.drawImage(
    originalImage,
    -originalImage.width / 2,
    -originalImage.height / 2,
  );
  canvasContext.restore();

  // 绘制遮罩
  canvasContext.fillStyle = "rgba(0, 0, 0, 0.5)";
  canvasContext.fillRect(0, 0, width, height);

  // 清除裁剪区域的遮罩
  canvasContext.globalCompositeOperation = "destination-out";
  canvasContext.fillRect(
    cropState.value.cropX,
    cropState.value.cropY,
    cropState.value.cropWidth,
    cropState.value.cropHeight,
  );
  canvasContext.globalCompositeOperation = "source-over";

  // 绘制裁剪框边框
  canvasContext.strokeStyle = "#409eff";
  canvasContext.lineWidth = 2;
  canvasContext.strokeRect(
    cropState.value.cropX,
    cropState.value.cropY,
    cropState.value.cropWidth,
    cropState.value.cropHeight,
  );

  // 绘制角落控制点
  const cornerSize = 8;
  const corners = [
    [cropState.value.cropX, cropState.value.cropY],
    [cropState.value.cropX + cropState.value.cropWidth, cropState.value.cropY],
    [cropState.value.cropX, cropState.value.cropY + cropState.value.cropHeight],
    [
      cropState.value.cropX + cropState.value.cropWidth,
      cropState.value.cropY + cropState.value.cropHeight,
    ],
  ];

  canvasContext.fillStyle = "#409eff";
  corners.forEach(([x, y]) => {
    canvasContext.fillRect(
      x - cornerSize / 2,
      y - cornerSize / 2,
      cornerSize,
      cornerSize,
    );
  });
};

// 更新预览
const updatePreview = () => {
  if (!canvasContext || !originalImage) return;

  const cropCanvas = document.createElement("canvas");
  const cropCtx = cropCanvas.getContext("2d");

  cropCanvas.width = props.avatarSize;
  cropCanvas.height = props.avatarSize;

  // 计算裁剪区域在原图中的位置
  const scaleX =
    originalImage.width / (cropCanvasRef.value.width / cropState.value.scale);
  const scaleY =
    originalImage.height / (cropCanvasRef.value.height / cropState.value.scale);

  const sourceX = (cropState.value.cropX / cropState.value.scale) * scaleX;
  const sourceY = (cropState.value.cropY / cropState.value.scale) * scaleY;
  const sourceWidth =
    (cropState.value.cropWidth / cropState.value.scale) * scaleX;
  const sourceHeight =
    (cropState.value.cropHeight / cropState.value.scale) * scaleY;

  // 绘制裁剪后的图片
  cropCtx.drawImage(
    originalImage,
    sourceX,
    sourceY,
    sourceWidth,
    sourceHeight,
    0,
    0,
    props.avatarSize,
    props.avatarSize,
  );

  // 更新圆形预览
  if (previewCircleRef.value) {
    previewCircleRef.value.innerHTML = "";
    const circleCanvas = cropCanvas.cloneNode();
    const circleCtx = circleCanvas.getContext("2d");
    circleCtx.drawImage(cropCanvas, 0, 0);
    circleCanvas.style.width = "100%";
    circleCanvas.style.height = "100%";
    circleCanvas.style.borderRadius = "50%";
    previewCircleRef.value.appendChild(circleCanvas);
  }

  // 更新方形预览
  if (previewSquareRef.value) {
    previewSquareRef.value.innerHTML = "";
    const squareCanvas = cropCanvas.cloneNode();
    const squareCtx = squareCanvas.getContext("2d");
    squareCtx.drawImage(cropCanvas, 0, 0);
    squareCanvas.style.width = "100%";
    squareCanvas.style.height = "100%";
    previewSquareRef.value.appendChild(squareCanvas);
  }
};

// 鼠标/触摸交互
const getEventPos = (event) => {
  const rect = cropCanvasRef.value.getBoundingClientRect();
  const clientX =
    event.clientX || (event.touches && event.touches[0]?.clientX) || 0;
  const clientY =
    event.clientY || (event.touches && event.touches[0]?.clientY) || 0;

  return {
    x: clientX - rect.left,
    y: clientY - rect.top,
  };
};

const startCrop = (event) => {
  event.preventDefault();
  const pos = getEventPos(event);

  cropState.value.isDragging = true;
  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;
};

const updateCrop = (event) => {
  if (!cropState.value.isDragging || cropState.value.isResizing) return;

  event.preventDefault();
  const pos = getEventPos(event);
  const deltaX = pos.x - cropState.value.startX;
  const deltaY = pos.y - cropState.value.startY;

  // 移动裁剪框
  const newX = cropState.value.cropX + deltaX;
  const newY = cropState.value.cropY + deltaY;

  // 边界检查
  const canvas = cropCanvasRef.value;
  if (newX >= 0 && newX + cropState.value.cropWidth <= canvas.width) {
    cropState.value.cropX = newX;
  }
  if (newY >= 0 && newY + cropState.value.cropHeight <= canvas.height) {
    cropState.value.cropY = newY;
  }

  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;

  drawCanvas();
  updatePreview();
};

const endCrop = (event) => {
  event.preventDefault();
  cropState.value.isDragging = false;
  cropState.value.isResizing = false;
};

// 调整裁剪框大小
const startResize = (type, event) => {
  event.preventDefault();
  event.stopPropagation();

  cropState.value.isResizing = true;
  cropState.value.resizeType = type;

  const pos = getEventPos(event);
  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;

  // 添加全局事件监听
  document.addEventListener("mousemove", handleResize);
  document.addEventListener("mouseup", endResize);
};

const handleResize = (event) => {
  if (!cropState.value.isResizing) return;

  event.preventDefault();
  const pos = getEventPos(event);
  const deltaX = pos.x - cropState.value.startX;
  const deltaY = pos.y - cropState.value.startY;

  const canvas = cropCanvasRef.value;
  const minSize = 50; // 最小裁剪框大小

  let newX = cropState.value.cropX;
  let newY = cropState.value.cropY;
  let newWidth = cropState.value.cropWidth;
  let newHeight = cropState.value.cropHeight;

  // 根据调整类型处理
  switch (cropState.value.resizeType) {
    case "tl": // 左上角
      newX += deltaX;
      newY += deltaY;
      newWidth -= deltaX;
      newHeight -= deltaY;
      break;
    case "tr": // 右上角
      newY += deltaY;
      newWidth += deltaX;
      newHeight -= deltaY;
      break;
    case "bl": // 左下角
      newX += deltaX;
      newWidth -= deltaX;
      newHeight += deltaY;
      break;
    case "br": // 右下角
      newWidth += deltaX;
      newHeight += deltaY;
      break;
    case "t": // 上边
      newY += deltaY;
      newHeight -= deltaY;
      break;
    case "r": // 右边
      newWidth += deltaX;
      break;
    case "b": // 下边
      newHeight += deltaY;
      break;
    case "l": // 左边
      newX += deltaX;
      newWidth -= deltaX;
      break;
  }

  // 保持正方形（可选）
  if (event.shiftKey) {
    const size = Math.min(newWidth, newHeight);
    newWidth = size;
    newHeight = size;
  }

  // 边界检查
  if (
    newWidth >= minSize &&
    newHeight >= minSize &&
    newX >= 0 &&
    newY >= 0 &&
    newX + newWidth <= canvas.width &&
    newY + newHeight <= canvas.height
  ) {
    cropState.value.cropX = newX;
    cropState.value.cropY = newY;
    cropState.value.cropWidth = newWidth;
    cropState.value.cropHeight = newHeight;

    cropState.value.startX = pos.x;
    cropState.value.startY = pos.y;

    drawCanvas();
    updatePreview();
  }
};

const endResize = (event) => {
  event.preventDefault();
  cropState.value.isResizing = false;
  cropState.value.resizeType = "";

  // 移除全局事件监听
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", endResize);
};

// 裁剪操作
const rotateLeft = () => {
  cropState.value.rotation -= 90;
  drawCanvas();
  updatePreview();
};

const rotateRight = () => {
  cropState.value.rotation += 90;
  drawCanvas();
  updatePreview();
};

const flipHorizontal = () => {
  // 简化实现：重置到初始状态
  resetCrop();
};

const flipVertical = () => {
  // 简化实现：重置到初始状态
  resetCrop();
};

const resetCrop = () => {
  if (!cropCanvasRef.value || !originalImage) return;

  const canvas = cropCanvasRef.value;
  const cropSize = Math.min(canvas.width, canvas.height) * 0.8;

  cropState.value.cropX = (canvas.width - cropSize) / 2;
  cropState.value.cropY = (canvas.height - cropSize) / 2;
  cropState.value.cropWidth = cropSize;
  cropState.value.cropHeight = cropSize;
  cropState.value.rotation = 0;

  drawCanvas();
  updatePreview();
};

// 重新选择文件
const reSelectFile = () => {
  triggerFileSelect();
};

// 确认裁剪
const handleConfirm = async () => {
  if (!canvasContext || !originalImage) return;

  try {
    processing.value = true;

    // 创建输出canvas
    const outputCanvas = document.createElement("canvas");
    const outputCtx = outputCanvas.getContext("2d");

    outputCanvas.width = props.outputSize;
    outputCanvas.height = props.outputSize;

    // 计算裁剪区域在原图中的位置
    const scaleX =
      originalImage.width / (cropCanvasRef.value.width / cropState.value.scale);
    const scaleY =
      originalImage.height /
      (cropCanvasRef.value.height / cropState.value.scale);

    const sourceX = (cropState.value.cropX / cropState.value.scale) * scaleX;
    const sourceY = (cropState.value.cropY / cropState.value.scale) * scaleY;
    const sourceWidth =
      (cropState.value.cropWidth / cropState.value.scale) * scaleX;
    const sourceHeight =
      (cropState.value.cropHeight / cropState.value.scale) * scaleY;

    // 绘制裁剪后的图片
    outputCtx.drawImage(
      originalImage,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      0,
      0,
      props.outputSize,
      props.outputSize,
    );

    // 转换为Blob
    const blob = await new Promise((resolve, reject) => {
      outputCanvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("图片处理失败"));
          }
        },
        props.outputFormat,
        props.outputQuality,
      );
    });

    // 生成预览URL
    const previewUrl = URL.createObjectURL(blob);
    currentAvatar.value = previewUrl;

    // 触发事件
    emit("update:modelValue", previewUrl);
    emit("crop-complete", {
      blob,
      canvas: outputCanvas,
      previewUrl,
      file: blob,
      size: blob.size,
    });

    cropDialogVisible.value = false;
    ElMessage.success("头像裁剪完成");
  } catch (error) {
    console.error("裁剪失败:", error);
    ElMessage.error("裁剪失败: " + error.message);
  } finally {
    processing.value = false;
  }
};

// 取消裁剪
const handleCancel = () => {
  cropDialogVisible.value = false;
  emit("crop-cancel");
};

// 对话框关闭处理
const handleDialogClose = () => {
  // 清理canvas和图片资源
  if (canvasContext) {
    canvasContext.clearRect(
      0,
      0,
      cropCanvasRef.value.width,
      cropCanvasRef.value.height,
    );
    canvasContext = null;
  }

  originalImage = null;

  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
    originalImageUrl.value = "";
  }

  // 重置裁剪状态
  cropState.value = {
    isDragging: false,
    isResizing: false,
    resizeType: "",
    startX: 0,
    startY: 0,
    cropX: 0,
    cropY: 0,
    cropWidth: 200,
    cropHeight: 200,
    scale: 1,
    rotation: 0,
  };

  // 清理事件监听器
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", endResize);
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
  }

  if (currentAvatar.value && currentAvatar.value.startsWith("blob:")) {
    URL.revokeObjectURL(currentAvatar.value);
  }
});

// 监听modelValue变化
import { watch } from "vue";
watch(
  () => props.modelValue,
  (newValue) => {
    currentAvatar.value = newValue;
  },
);
</script>

<style scoped>
.avatar-uploader {
  display: inline-block;
}

.avatar-display {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.avatar-display:hover {
  transform: scale(1.02) translateY(-1px);
}

.avatar-container {
  position: relative;
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.avatar-container:hover {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: none;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.placeholder-text {
  margin-top: 10px;
  text-align: center;
  line-height: 1.3;
  font-weight: 400;
  opacity: 0.8;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(64, 158, 255, 0.9) 0%,
    rgba(103, 194, 58, 0.9) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  font-weight: 500;
  pointer-events: none;
  backdrop-filter: blur(2px);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
  transform: scale(1.02);
}

.overlay-text {
  margin-top: 6px;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 裁剪对话框样式 */
.avatar-crop-dialog {
  --el-dialog-padding-primary: 20px;
}

.crop-container {
  display: flex;
  gap: 20px;
  height: 450px;
  min-height: 450px;
}

.crop-area {
  flex: 2;
  min-width: 0;
}

.crop-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.crop-canvas {
  max-width: 100%;
  max-height: 100%;
  display: block;
  cursor: move;
  border-radius: 4px;
}

.crop-canvas:active {
  cursor: grabbing;
}

/* 裁剪框调整控制点 */
.crop-resize-handles {
  position: absolute;
  pointer-events: none;
  border: 2px solid #409eff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
}

.resize-handle {
  position: absolute;
  background: #409eff;
  border: 2px solid #ffffff;
  border-radius: 50%;
  pointer-events: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.resize-handle:hover {
  background: #337ecc;
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

/* 角落控制点 */
.corner-tl,
.corner-tr,
.corner-bl,
.corner-br {
  width: 12px;
  height: 12px;
}

.corner-tl {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.corner-tr {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.corner-bl {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.corner-br {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* 边缘控制点 */
.edge-t,
.edge-r,
.edge-b,
.edge-l {
  width: 10px;
  height: 10px;
}

.edge-t {
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.edge-r {
  right: -5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

.edge-b {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.edge-l {
  left: -5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
}

.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.preview-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.preview-circle,
.preview-square {
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border: 2px solid #e4e7ed;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-circle {
  border-radius: 50%;
}

.preview-square {
  border-radius: 8px;
}

.preview-size {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.crop-controls {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crop-container {
    flex-direction: column;
    height: auto;
    gap: 16px;
  }

  .crop-area {
    height: 300px;
  }

  .preview-area {
    flex-direction: row;
    justify-content: center;
    gap: 20px;
  }

  .dialog-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .crop-controls {
    order: 2;
  }

  .action-buttons {
    order: 1;
    justify-content: center;
  }
}

/* 加载状态 */
.avatar-container.loading {
  pointer-events: none;
  opacity: 0.6;
}

.avatar-container.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #409eff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 禁用状态 */
.avatar-uploader.disabled .avatar-display {
  cursor: not-allowed;
  opacity: 0.6;
}

.avatar-uploader.disabled .avatar-container:hover {
  transform: none;
  border-color: #e4e7ed;
  box-shadow: none;
}

.avatar-uploader.disabled .avatar-overlay {
  display: none;
}
</style>
