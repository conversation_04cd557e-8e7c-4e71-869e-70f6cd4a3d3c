/**
 * 缓存中间件
 * 提供HTTP响应缓存功能，支持ETag、Last-Modified等缓存策略
 */

const { cacheManager } = require('../utils/cache');
const { ResponseUtils } = require('../utils/helpers');

/**
 * 响应缓存中间件
 * @param {Object} options - 缓存配置选项
 * @param {number} options.ttl - 缓存时间（毫秒）
 * @param {string} options.type - 缓存类型
 * @param {function} options.keyGenerator - 缓存键生成函数
 * @param {function} options.shouldCache - 是否缓存判断函数
 */
const responseCache = (options = {}) => {
  const {
    ttl = 300000, // 5分钟
    type = 'api',
    keyGenerator = (req) => `${req.method}:${req.path}:${JSON.stringify(req.query)}`,
    shouldCache = (req, res) => req.method === 'GET' && res.statusCode === 200
  } = options;

  return async (req, res, next) => {
    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }

    const cacheKey = keyGenerator(req);

    // 尝试从缓存获取
    const cachedResponse = cacheManager.get(type, cacheKey);
    if (cachedResponse) {
      // 设置缓存头
      res.set('X-Cache', 'HIT');
      res.set('X-Cache-Key', cacheKey);

      // 返回缓存的响应
      return res.status(cachedResponse.status).json(cachedResponse.data);
    }

    // 劫持响应对象
    const originalJson = res.json;
    const originalStatus = res.status;
    let responseStatus = 200;
    let responseData = null;

    // 重写status方法
    res.status = function (code) {
      responseStatus = code;
      return originalStatus.call(this, code);
    };

    // 重写json方法
    res.json = function (data) {
      responseData = data;

      // 检查是否应该缓存
      if (shouldCache(req, res)) {
        const cacheData = {
          status: responseStatus,
          data: data,
          timestamp: Date.now(),
          ttl: ttl
        };

        // 缓存响应
        cacheManager.set(type, cacheKey, cacheData, ttl);

        // 设置缓存头
        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        res.set('Cache-Control', `public, max-age=${Math.floor(ttl / 1000)}`);
      }

      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * ETag缓存中间件
 * 基于响应内容生成ETag，支持304 Not Modified
 */
const etagCache = () => {
  return (req, res, next) => {
    const originalJson = res.json;

    res.json = function (data) {
      const etag = `"${Buffer.from(JSON.stringify(data)).toString('base64')}"`;

      // 设置ETag头
      res.set('ETag', etag);

      // 检查If-None-Match头
      const ifNoneMatch = req.headers['if-none-match'];
      if (ifNoneMatch === etag) {
        res.status(304).end();
        return;
      }

      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Last-Modified缓存中间件
 * 基于资源修改时间的缓存策略
 */
const lastModifiedCache = (getLastModified) => {
  return (req, res, next) => {
    const originalJson = res.json;

    res.json = function (data) {
      const lastModified = getLastModified(data);

      if (lastModified) {
        const lastModifiedDate = new Date(lastModified);
        res.set('Last-Modified', lastModifiedDate.toUTCString());

        // 检查If-Modified-Since头
        const ifModifiedSince = req.headers['if-modified-since'];
        if (ifModifiedSince) {
          const ifModifiedSinceDate = new Date(ifModifiedSince);
          if (lastModifiedDate <= ifModifiedSinceDate) {
            res.status(304).end();
            return;
          }
        }
      }

      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * 缓存预热中间件
 * 在应用启动时预热常用的缓存数据
 */
const warmupCache = async () => {
  console.log('🔥 开始缓存预热...');

  try {
    const { ContractModel, UserModel } = require('../utils/database');
    const { cacheManager, CacheHelpers } = require('../utils/cache');

    // 预热用户角色统计
    console.log('  📊 预热用户角色统计...');
    const userStats = await UserModel.getRoleStats();
    await CacheHelpers.cacheStatistics('user_roles', 'all', userStats, 600000);

    // 预热合同状态统计
    console.log('  📋 预热合同状态统计...');
    const contractStats = await ContractModel.getStatusStats();
    await CacheHelpers.cacheStatistics('contract_status', 'all', contractStats, 600000);

    // 预热活跃用户列表
    console.log('  👥 预热活跃用户列表...');
    const activeUsers = await UserModel.getActiveUsers(50);
    cacheManager.set('users', 'active_users_list', activeUsers, 300000);

    // 预热最近合同列表
    console.log('  📄 预热最近合同列表...');
    const recentContracts = await ContractModel.getRecent(20);
    cacheManager.set('contracts', 'recent_contracts_list', recentContracts, 180000);

    console.log('✅ 缓存预热完成');
  } catch (error) {
    console.error('❌ 缓存预热失败:', error);
  }
};

/**
 * 缓存失效中间件
 * 在数据变更时自动清理相关缓存
 */
const cacheInvalidation = (type, patterns = []) => {
  return (req, res, next) => {
    const originalJson = res.json;

    res.json = function (data) {
      // 在响应成功后清理缓存
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // 清理指定类型的缓存
        if (type) {
          cacheManager.clearType(type);
        }

        // 清理匹配模式的缓存
        patterns.forEach(pattern => {
          if (typeof pattern === 'string') {
            cacheManager.delete('api', pattern);
          } else if (typeof pattern === 'function') {
            pattern(req, res);
          }
        });
      }

      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * 缓存控制中间件
 * 提供缓存控制相关的HTTP头设置
 */
const cacheControl = (options = {}) => {
  const {
    maxAge = 300, // 5分钟
    sMaxAge = null,
    mustRevalidate = false,
    noCache = false,
    noStore = false,
    isPrivate = false,
    isPublic = true
  } = options;

  return (req, res, next) => {
    if (noStore) {
      res.set('Cache-Control', 'no-store');
    } else if (noCache) {
      res.set('Cache-Control', 'no-cache');
    } else {
      const directives = [];

      if (isPrivate) {
        directives.push('private');
      } else if (isPublic) {
        directives.push('public');
      }

      if (maxAge !== null) {
        directives.push(`max-age=${maxAge}`);
      }

      if (sMaxAge !== null) {
        directives.push(`s-maxage=${sMaxAge}`);
      }

      if (mustRevalidate) {
        directives.push('must-revalidate');
      }

      if (directives.length > 0) {
        res.set('Cache-Control', directives.join(', '));
      }
    }

    next();
  };
};

/**
 * 缓存统计中间件
 * 记录缓存的使用情况
 */
const cacheStats = () => {
  return (req, res, next) => {
    const cacheHit = res.get('X-Cache') === 'HIT';

    // 记录缓存统计
    if (cacheHit) {
      console.log(`Cache HIT: ${req.method} ${req.path}`);
    } else {
      console.log(`Cache MISS: ${req.method} ${req.path}`);
    }

    next();
  };
};

module.exports = {
  responseCache,
  etagCache,
  lastModifiedCache,
  warmupCache,
  cacheInvalidation,
  cacheControl,
  cacheStats
};