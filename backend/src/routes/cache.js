/**
 * 缓存管理路由
 * 提供缓存统计、清理、管理等功能
 */

const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { cacheManager } = require('../utils/cache');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS } = require('../utils/constants');
const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

const router = express.Router();

/**
 * 获取缓存统计信息
 * GET /api/cache/stats
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以查看缓存统计
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const stats = cacheManager.getStats();
    
    // 添加系统信息
    const systemInfo = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      platform: process.platform
    };

    const response = {
      cache: stats,
      system: systemInfo,
      timestamp: new Date().toISOString()
    };

    res.json(ResponseUtils.success(response, '缓存统计获取成功'));

  } catch (error) {
    console.error('获取缓存统计失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('获取缓存统计失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 清理指定类型的缓存
 * DELETE /api/cache/:type
 */
router.delete('/:type', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以清理缓存
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const { type } = req.params;
    const validTypes = ['users', 'contracts', 'statistics', 'search', 'files'];

    if (!validTypes.includes(type)) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('无效的缓存类型', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const success = cacheManager.clearType(type);
    
    if (success) {
      // 记录操作日志
      await logOperation({
        userId: req.user.id,
        action: ACTIONS.DELETE,
        resourceType: RESOURCE_TYPES.SYSTEM,
        resourceId: `cache_${type}`,
        details: JSON.stringify({
          action: 'clear_cache',
          cacheType: type
        }),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        status: 'success'
      });

      res.json(ResponseUtils.success(null, `${type}缓存清理成功`));
    } else {
      res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('缓存类型不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

  } catch (error) {
    console.error('清理缓存失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('清理缓存失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 清理所有缓存
 * DELETE /api/cache
 */
router.delete('/', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以清理所有缓存
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    cacheManager.clearAll();
    
    // 记录操作日志
    await logOperation({
      userId: req.user.id,
      action: ACTIONS.DELETE,
      resourceType: RESOURCE_TYPES.SYSTEM,
      resourceId: 'cache_all',
      details: JSON.stringify({
        action: 'clear_all_cache'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(null, '所有缓存清理成功'));

  } catch (error) {
    console.error('清理所有缓存失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('清理所有缓存失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 重置缓存统计
 * POST /api/cache/reset-stats
 */
router.post('/reset-stats', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以重置缓存统计
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    cacheManager.resetStats();
    
    // 记录操作日志
    await logOperation({
      userId: req.user.id,
      action: ACTIONS.UPDATE,
      resourceType: RESOURCE_TYPES.SYSTEM,
      resourceId: 'cache_stats',
      details: JSON.stringify({
        action: 'reset_cache_stats'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(null, '缓存统计重置成功'));

  } catch (error) {
    console.error('重置缓存统计失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('重置缓存统计失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取缓存配置
 * GET /api/cache/config
 */
router.get('/config', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以查看缓存配置
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const config = {
      defaultTTL: cacheManager.defaultTTL,
      checkInterval: cacheManager.checkInterval,
      maxSize: cacheManager.maxSize,
      cacheTypes: Object.keys(cacheManager.caches),
      isActive: true
    };

    res.json(ResponseUtils.success(config, '缓存配置获取成功'));

  } catch (error) {
    console.error('获取缓存配置失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('获取缓存配置失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 手动清理过期缓存
 * POST /api/cache/cleanup
 */
router.post('/cleanup', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以手动清理过期缓存
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    cacheManager.cleanup();
    
    // 记录操作日志
    await logOperation({
      userId: req.user.id,
      action: ACTIONS.UPDATE,
      resourceType: RESOURCE_TYPES.SYSTEM,
      resourceId: 'cache_cleanup',
      details: JSON.stringify({
        action: 'manual_cache_cleanup'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(null, '过期缓存清理完成'));

  } catch (error) {
    console.error('清理过期缓存失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('清理过期缓存失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取缓存详情
 * GET /api/cache/details
 */
router.get('/details', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以查看缓存详情
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const details = {};
    
    // 获取各个缓存类型的详细信息
    Object.entries(cacheManager.caches).forEach(([type, cache]) => {
      details[type] = {
        size: cache.size(),
        maxSize: cache.maxSize,
        utilization: ((cache.size() / cache.maxSize) * 100).toFixed(2) + '%'
      };
    });

    // 获取TTL信息
    const ttlInfo = {
      totalEntries: cacheManager.ttlMap.size,
      nextExpiration: null
    };

    if (cacheManager.ttlMap.size > 0) {
      const nextExpiration = Math.min(...cacheManager.ttlMap.values());
      ttlInfo.nextExpiration = new Date(nextExpiration).toISOString();
    }

    const response = {
      cacheDetails: details,
      ttlInfo,
      timestamp: new Date().toISOString()
    };

    res.json(ResponseUtils.success(response, '缓存详情获取成功'));

  } catch (error) {
    console.error('获取缓存详情失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('获取缓存详情失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

module.exports = router;