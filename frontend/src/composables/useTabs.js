/**
 * Tab 管理 Composable
 * 管理多标签页的状态和操作
 */

import { ref, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";

// Tab状态持久化的key
const TABS_STORAGE_KEY = "contract-review-tabs";
const ACTIVE_TAB_STORAGE_KEY = "contract-review-active-tab";

// 从localStorage恢复Tab状态
const restoreTabsFromStorage = () => {
  try {
    const savedTabs = localStorage.getItem(TABS_STORAGE_KEY);
    const savedActiveTab = localStorage.getItem(ACTIVE_TAB_STORAGE_KEY);

    if (savedTabs) {
      const parsedTabs = JSON.parse(savedTabs);

      return {
        tabs: parsedTabs,
        activeTab: savedActiveTab || "home",
      };
    }
  } catch (error) {
    console.warn("恢复Tab状态失败:", error);
  }

  return {
    tabs: [],
    activeTab: "home",
  };
};

// 保存Tab状态到localStorage
const saveTabsToStorage = (tabsData, activeTabData) => {
  try {
    localStorage.setItem(TABS_STORAGE_KEY, JSON.stringify(tabsData));
    localStorage.setItem(ACTIVE_TAB_STORAGE_KEY, activeTabData);
  } catch (error) {
    console.warn("保存Tab状态失败:", error);
  }
};

// 初始化状态
const initialState = restoreTabsFromStorage();
const tabs = ref(initialState.tabs);
const activeTab = ref(initialState.activeTab);

/**
 * Tab 管理 Hook
 */
export function useTabs() {
  const router = useRouter();
  const route = useRoute();

  /**
   * 打开新标签页
   * @param {Object} menuItem - 菜单项对象
   */
  const openTab = (menuItem) => {
    const { key, title, path, component, icon, params } = menuItem;

    // 检查标签页是否已存在
    const existingTab = tabs.value.find((tab) => tab.name === key);

    if (existingTab) {
      // 如果已存在，激活该标签页，完全不进行路由跳转

      activeTab.value = key;
    } else {
      // 创建新标签页，不进行任何路由操作

      const newTab = {
        name: key,
        title,
        path,
        component,
        icon,
        params, // 支持传递参数
        closable: key !== "home", // 首页不可关闭
        modified: false,
      };

      tabs.value.push(newTab);
      activeTab.value = key;
    }

    // 保存Tab状态到localStorage
    saveTabsToStorage(tabs.value, activeTab.value);

    // 确保不触发任何路由变化
  };

  /**
   * 关闭标签页
   * @param {string} tabName - 标签页名称
   */
  const closeTab = (tabName) => {
    const tabIndex = tabs.value.findIndex((tab) => tab.name === tabName);

    if (tabIndex === -1) return;

    // 移除标签页
    tabs.value.splice(tabIndex, 1);

    // 如果关闭的是当前激活的标签页，需要激活其他标签页
    if (activeTab.value === tabName) {
      if (tabs.value.length > 0) {
        // 优先激活右侧标签页，如果没有则激活左侧
        const nextTab = tabs.value[tabIndex] || tabs.value[tabIndex - 1];
        if (nextTab) {
          activeTab.value = nextTab.name;
          // 不进行路由跳转，保持页面状态
        }
      } else {
        // 没有标签页了，激活默认首页Tab
        activeTab.value = "home";
        // 确保首页Tab存在
        if (!tabs.value.find((tab) => tab.name === "home")) {
          openTab({
            key: "home",
            title: "首页",
            path: "/dashboard",
            component: "HomePage",
            icon: "House",
          });
          return; // openTab会自动保存状态，这里直接返回
        }
      }
    }

    // 保存Tab状态到localStorage
    saveTabsToStorage(tabs.value, activeTab.value);
  };

  /**
   * 关闭其他标签页
   * @param {string} keepTabName - 保留的标签页名称
   */
  const closeOtherTabs = (keepTabName) => {
    tabs.value = tabs.value.filter(
      (tab) => tab.name === keepTabName || tab.closable === false,
    );

    if (activeTab.value !== keepTabName) {
      const keepTab = tabs.value.find((tab) => tab.name === keepTabName);
      if (keepTab) {
        activeTab.value = keepTabName;
        router.push(keepTab.path);
      }
    }
  };

  /**
   * 关闭所有标签页
   */
  const closeAllTabs = () => {
    // 只保留不可关闭的标签页
    tabs.value = tabs.value.filter((tab) => tab.closable === false);

    if (tabs.value.length > 0) {
      const firstTab = tabs.value[0];
      activeTab.value = firstTab.name;
      router.push(firstTab.path);
    } else {
      activeTab.value = "";
      router.push("/dashboard");
    }
  };

  /**
   * 设置标签页修改状态
   * @param {string} tabName - 标签页名称
   * @param {boolean} modified - 是否已修改
   */
  const setTabModified = (tabName, modified = true) => {
    const tab = tabs.value.find((tab) => tab.name === tabName);
    if (tab) {
      tab.modified = modified;
    }
  };

  /**
   * 刷新标签页
   * @param {string} tabName - 标签页名称
   */
  const refreshTab = (tabName) => {
    const tab = tabs.value.find((tab) => tab.name === tabName);
    if (tab) {
      // 重置修改状态
      tab.modified = false;

      // 如果是当前激活的标签页，刷新路由
      if (activeTab.value === tabName) {
        router.replace({
          path: "/redirect" + tab.path,
        });
      }
    }
  };

  /**
   * 设置当前激活的标签页
   * @param {string} tabName - 标签页名称
   */
  const setActiveTab = (tabName) => {
    activeTab.value = tabName;
    // 保存激活Tab状态
    saveTabsToStorage(tabs.value, activeTab.value);
  };

  /**
   * 根据路径查找标签页
   * @param {string} path - 路径
   * @returns {Object|null} 标签页对象
   */
  const findTabByPath = (path) => {
    return tabs.value.find((tab) => tab.path === path) || null;
  };

  /**
   * 根据名称查找标签页
   * @param {string} name - 标签页名称
   * @returns {Object|null} 标签页对象
   */
  const findTabByName = (name) => {
    return tabs.value.find((tab) => tab.name === name) || null;
  };

  /**
   * 获取当前激活的标签页
   * @returns {Object|null} 当前标签页对象
   */
  const getCurrentTab = () => {
    return findTabByName(activeTab.value);
  };

  /**
   * 检查标签页是否存在
   * @param {string} tabName - 标签页名称
   * @returns {boolean} 是否存在
   */
  const hasTab = (tabName) => {
    return tabs.value.some((tab) => tab.name === tabName);
  };

  /**
   * 获取标签页数量
   * @returns {number} 标签页数量
   */
  const tabCount = computed(() => tabs.value.length);

  /**
   * 获取可关闭的标签页数量
   * @returns {number} 可关闭的标签页数量
   */
  const closableTabCount = computed(() => {
    return tabs.value.filter((tab) => tab.closable !== false).length;
  });

  /**
   * 检查是否有未保存的修改
   * @returns {boolean} 是否有未保存的修改
   */
  const hasUnsavedChanges = computed(() => {
    return tabs.value.some((tab) => tab.modified);
  });

  /**
   * 获取有未保存修改的标签页
   * @returns {Array} 有修改的标签页列表
   */
  const modifiedTabs = computed(() => {
    return tabs.value.filter((tab) => tab.modified);
  });

  // 注释掉路由变化监听，避免Tab切换时的页面刷新
  // 现在Tab状态完全由用户交互控制，不依赖路由变化
  // watch(
  //   () => route.path,
  //   (newPath) => {
  //     const tab = findTabByPath(newPath);
  //     if (tab) {
  //       activeTab.value = tab.name;
  //     }
  //   },
  //   { immediate: true },
  // );

  // 初始化默认标签页
  const initDefaultTab = () => {
    if (tabs.value.length === 0) {
      openTab({
        key: "home",
        title: "首页",
        path: "/dashboard",
        component: "HomePage",
        icon: "House",
      });
    } else {
      // 已恢复Tab状态
    }
    // 验证activeTab是否存在于tabs中
    const activeTabExists = tabs.value.find(
      (tab) => tab.name === activeTab.value,
    );
    if (!activeTabExists && tabs.value.length > 0) {
      activeTab.value = tabs.value[0].name;
      saveTabsToStorage(tabs.value, activeTab.value);
    }
  };

  // 清除Tab状态（用于登出等场景）
  const clearTabsStorage = () => {
    localStorage.removeItem(TABS_STORAGE_KEY);
    localStorage.removeItem(ACTIVE_TAB_STORAGE_KEY);
    tabs.value = [];
    activeTab.value = "home";
  };

  /**
   * 打开合同详情Tab
   * @param {Object} contract - 合同对象
   */
  const openContractDetailTab = (contract) => {
    const tabKey = `contract-detail-${contract.id}`;
    const tabTitle = `合同详情 - ${contract.serial_number}`;

    openTab({
      key: tabKey,
      title: tabTitle,
      path: `/contract/${contract.id}`,
      component: "contract-detail",
      icon: "Document",
      params: { contractId: contract.id, contract },
    });
  };

  return {
    // 状态
    tabs: computed(() => tabs.value),
    activeTab: computed(() => activeTab.value),
    tabCount,
    closableTabCount,
    hasUnsavedChanges,
    modifiedTabs,

    // 方法
    openTab,
    closeTab,
    closeOtherTabs,
    closeAllTabs,
    setTabModified,
    refreshTab,
    setActiveTab,
    findTabByPath,
    findTabByName,
    getCurrentTab,
    hasTab,
    initDefaultTab,
    clearTabsStorage,
    openContractDetailTab,
  };
}
