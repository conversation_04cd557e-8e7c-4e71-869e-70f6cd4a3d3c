/**
 * 全局状态管理
 * 使用 Vue 3 Composition API 实现简单的状态管理
 */

import { reactive, readonly, computed } from "vue";

// 全局状态
const state = reactive({
  // 用户信息
  user: null,
  token: localStorage.getItem("token") || null,

  // 应用设置
  settings: {
    theme: "light",
    language: "zh-CN",
    sidebarCollapsed: false,
    autoRefresh: true,
    refreshInterval: 5 * 60 * 1000, // 5分钟
  },

  // 通知消息
  notifications: [],

  // 加载状态
  loading: {
    global: false,
    user: false,
    contracts: false,
    upload: false,
  },

  // 缓存数据
  cache: {
    contracts: new Map(),
    users: new Map(),
    stats: null,
    lastUpdate: null,
  },

  // 系统状态
  system: {
    online: navigator.onLine,
    version: "1.0.0",
    lastSync: null,
  },
});

// 状态操作方法
const mutations = {
  // 用户相关
  setUser(user) {
    state.user = user;
  },

  setToken(token) {
    state.token = token;
    if (token) {
      localStorage.setItem("token", token);
    } else {
      localStorage.removeItem("token");
    }
  },

  clearAuth() {
    state.user = null;
    state.token = null;
    localStorage.removeItem("token");
  },

  // 设置相关
  updateSettings(newSettings) {
    Object.assign(state.settings, newSettings);
    localStorage.setItem("app-settings", JSON.stringify(state.settings));
  },

  setSidebarCollapsed(collapsed) {
    state.settings.sidebarCollapsed = collapsed;
    localStorage.setItem("sidebar-collapsed", collapsed.toString());
  },

  // 通知相关
  addNotification(notification) {
    const id = Date.now() + Math.random();
    state.notifications.push({
      id,
      ...notification,
      timestamp: new Date().toISOString(),
    });
    return id;
  },

  removeNotification(id) {
    const index = state.notifications.findIndex((n) => n.id === id);
    if (index > -1) {
      state.notifications.splice(index, 1);
    }
  },

  clearNotifications() {
    state.notifications.length = 0;
  },

  markNotificationRead(id) {
    const notification = state.notifications.find((n) => n.id === id);
    if (notification) {
      notification.read = true;
    }
  },

  // 加载状态
  setLoading(key, loading) {
    if (key in state.loading) {
      state.loading[key] = loading;
    }
  },

  // 缓存相关
  setCacheData(key, data) {
    state.cache[key] = data;
    state.cache.lastUpdate = new Date().toISOString();
  },

  clearCache() {
    state.cache.contracts.clear();
    state.cache.users.clear();
    state.cache.stats = null;
    state.cache.lastUpdate = null;
  },

  // 系统状态
  setOnlineStatus(online) {
    state.system.online = online;
  },

  updateLastSync() {
    state.system.lastSync = new Date().toISOString();
  },
};

// 计算属性
const getters = {
  // 用户相关
  isAuthenticated: computed(() => !!state.token && !!state.user),
  userRole: computed(() => state.user?.role || null),
  userName: computed(() => state.user?.username || ""),
  userDisplayName: computed(
    () => state.user?.real_name || state.user?.username || "",
  ),

  // 通知相关
  unreadNotifications: computed(() =>
    state.notifications.filter((n) => !n.read),
  ),
  unreadCount: computed(
    () => state.notifications.filter((n) => !n.read).length,
  ),

  // 加载状态
  isLoading: computed(() =>
    Object.values(state.loading).some((loading) => loading),
  ),

  // 系统状态
  isOnline: computed(() => state.system.online),
  canSync: computed(() => state.system.online && state.token),

  // 缓存状态
  cacheAge: computed(() => {
    if (!state.cache.lastUpdate) return null;
    return Date.now() - new Date(state.cache.lastUpdate).getTime();
  }),

  isCacheStale: computed(() => {
    const age = getters.cacheAge.value;
    return age === null || age > 10 * 60 * 1000; // 10分钟
  }),
};

// 操作方法
const actions = {
  // 初始化应用
  async initApp() {
    // 恢复设置
    const savedSettings = localStorage.getItem("app-settings");
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        mutations.updateSettings(settings);
      } catch (error) {
        console.warn("恢复设置失败:", error);
      }
    }

    // 恢复侧边栏状态
    const sidebarCollapsed = localStorage.getItem("sidebar-collapsed");
    if (sidebarCollapsed !== null) {
      mutations.setSidebarCollapsed(sidebarCollapsed === "true");
    }

    // 监听网络状态
    window.addEventListener("online", () => mutations.setOnlineStatus(true));
    window.addEventListener("offline", () => mutations.setOnlineStatus(false));
  },

  // 登录
  // 登录
  async login(credentials) {
    mutations.setLoading("user", true);
    try {
      // 这里应该调用实际的登录API
      // const response = await authAPI.login(credentials)
      // mutations.setToken(response.token)
      // mutations.setUser(response.user)

      // 临时模拟
      mutations.setToken("mock-token");
      mutations.setUser({
        id: 1,
        username: credentials.username,
        role: "employee",
      });

      mutations.updateLastSync();
      return true;
    } finally {
      mutations.setLoading("user", false);
    }
  },

  // 登出
  async logout() {
    mutations.clearAuth();
    mutations.clearCache();
    mutations.clearNotifications();
  },

  // 显示通知
  showNotification(notification) {
    return mutations.addNotification(notification);
  },

  // 显示成功消息
  showSuccess(message) {
    return actions.showNotification({
      type: "success",
      title: "成功",
      message,
      duration: 3000,
    });
  },

  // 显示错误消息
  showError(message) {
    return actions.showNotification({
      type: "error",
      title: "错误",
      message,
      duration: 5000,
    });
  },

  // 显示警告消息
  showWarning(message) {
    return actions.showNotification({
      type: "warning",
      title: "警告",
      message,
      duration: 4000,
    });
  },

  // 显示信息消息
  showInfo(message) {
    return actions.showNotification({
      type: "info",
      title: "提示",
      message,
      duration: 3000,
    });
  },

  // 同步数据
  async syncData() {
    if (!getters.canSync.value) return;

    mutations.setLoading("global", true);
    try {
      // 这里应该同步各种数据
      // await Promise.all([
      //   contractsAPI.getStats(),
      //   dashboardAPI.getNotifications(),
      //   // 其他数据同步
      // ])

      mutations.updateLastSync();
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("数据同步失败:", error);
      }
      actions.showError("数据同步失败");
    } finally {
      mutations.setLoading("global", false);
    }
  },
};

// 导出状态管理
export const useStore = () => ({
  // 只读状态
  state: readonly(state),

  // 计算属性
  ...getters,

  // 操作方法
  ...actions,

  // 直接暴露的mutations（谨慎使用）
  mutations,
});

// 默认导出
export default useStore;
