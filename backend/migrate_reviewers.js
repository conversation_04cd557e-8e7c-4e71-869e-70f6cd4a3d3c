/**
 * 迁移脚本：将旧的审核员账号迁移到县级或市级审核员
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
    db.close();
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
    db.close();
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
    db.close();
  });
}

// 主迁移函数
async function migrateReviewers() {
  try {
    console.log('🔄 开始迁移审核员账号...');

    // 1. 查找所有旧的reviewer角色用户
    const oldReviewers = await allQuery(`
      SELECT id, username, role 
      FROM users 
      WHERE role = 'reviewer' AND status = 'active'
    `);

    console.log(`📋 找到 ${oldReviewers.length} 个旧的审核员账号`);

    if (oldReviewers.length === 0) {
      console.log('✅ 没有需要迁移的审核员账号');
      return;
    }

    // 2. 迁移策略：
    // - reviewer -> county_reviewer (县级审核员)
    // - reviewer2 -> city_reviewer (市级审核员)
    // - 其他reviewer -> county_reviewer (默认县级)

    for (const reviewer of oldReviewers) {
      let newRole = 'county_reviewer'; // 默认迁移到县级

      // 根据用户名决定迁移到哪个级别
      if (reviewer.username === 'reviewer2') {
        newRole = 'city_reviewer';
      }

      // 更新用户角色
      const result = await runQuery(`
        UPDATE users 
        SET role = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, [newRole, reviewer.id]);

      if (result.changes > 0) {
        console.log(`✅ 迁移用户 ${reviewer.username}: ${reviewer.role} -> ${newRole}`);
      } else {
        console.log(`⚠️  迁移用户 ${reviewer.username} 失败`);
      }
    }

    // 3. 验证迁移结果
    const migratedUsers = await allQuery(`
      SELECT username, role 
      FROM users 
      WHERE role IN ('county_reviewer', 'city_reviewer') 
      ORDER BY role, username
    `);

    console.log('\n📊 迁移后的审核员分布:');
    console.table(migratedUsers);

    console.log('🎉 审核员账号迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrateReviewers();
}

module.exports = { migrateReviewers };
