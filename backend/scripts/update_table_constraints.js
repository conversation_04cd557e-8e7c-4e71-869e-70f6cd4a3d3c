/**
 * 更新数据库表约束脚本：移除reviewing状态约束
 * 运行方式：node backend/scripts/update_table_constraints.js
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const dbPath = DATABASE_CONFIG.path;

console.log('开始更新数据库表约束...');
console.log('数据库路径:', dbPath);

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 执行表结构更新
async function updateTableConstraints() {
  return new Promise((resolve, reject) => {
    // SQLite不支持直接修改CHECK约束，需要重建表
    const steps = [
      // 1. 创建新表
      `CREATE TABLE contracts_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        serial_number TEXT UNIQUE NOT NULL,
        submitter_id INTEGER NOT NULL,
        reviewer_id INTEGER,
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
        submit_note TEXT,
        review_comment TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        reviewed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        review_level TEXT,
        FOREIGN KEY (submitter_id) REFERENCES users(id),
        FOREIGN KEY (reviewer_id) REFERENCES users(id)
      )`,

      // 2. 复制数据
      `INSERT INTO contracts_new 
       SELECT id, serial_number, submitter_id, reviewer_id, filename, file_path, 
              file_size, status, submit_note, review_comment, submitted_at, 
              reviewed_at, created_at, updated_at, review_level
       FROM contracts`,

      // 3. 删除旧表
      `DROP TABLE contracts`,

      // 4. 重命名新表
      `ALTER TABLE contracts_new RENAME TO contracts`
    ];

    let currentStep = 0;

    function executeNextStep() {
      if (currentStep >= steps.length) {
        console.log('✅ 表约束更新完成');
        resolve();
        return;
      }

      const sql = steps[currentStep];
      console.log(`执行步骤 ${currentStep + 1}/${steps.length}...`);

      db.run(sql, function (err) {
        if (err) {
          console.error(`步骤 ${currentStep + 1} 执行失败:`, err.message);
          reject(err);
          return;
        }

        console.log(`✅ 步骤 ${currentStep + 1} 完成`);
        currentStep++;
        executeNextStep();
      });
    }

    executeNextStep();
  });
}

// 执行更新并关闭数据库连接
updateTableConstraints()
  .then(() => {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
      process.exit(0);
    });
  })
  .catch((err) => {
    console.error('❌ 更新失败:', err);
    db.close();
    process.exit(1);
  });
