/**
 * 用户状态管理 Pinia Store
 * 管理用户认证、个人资料、角色和权限
 */

import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { ElMessage } from "element-plus";
import { authAPI } from "@/api/auth";

export const useUserStore = defineStore("user", () => {
  // ===== 状态 =====
  const user = ref(null);
  const token = ref(localStorage.getItem("token") || null);
  const loading = ref(false);
  const permissions = ref([]);
  const lastLoginTime = ref(null);

  // ===== 计算属性 (Getters) =====
  const isAuthenticated = computed(() => !!token.value && !!user.value);

  const userRole = computed(() => user.value?.role || null);

  const userName = computed(() => user.value?.username || "");

  const userDisplayName = computed(
    () => user.value?.real_name || user.value?.username || "",
  );

  const isAdmin = computed(() => userRole.value === "admin");

  const isReviewer = computed(() =>
    ["county_reviewer", "city_reviewer"].includes(userRole.value),
  );

  const isEmployee = computed(() => userRole.value === "employee");

  const hasPermission = computed(() => (permission) => {
    if (isAdmin.value) return true;
    return permissions.value.includes(permission);
  });

  const canAccessRoute = computed(() => (routeRole) => {
    if (!routeRole) return true;
    if (Array.isArray(routeRole)) {
      return routeRole.includes(userRole.value);
    }
    return userRole.value === routeRole;
  });

  // ===== 操作方法 (Actions) =====

  /**
   * 初始化用户状态
   * 从localStorage恢复用户信息并验证token
   */
  const initAuth = async () => {
    try {
      const savedToken = localStorage.getItem("token");
      const savedUser = localStorage.getItem("user");

      if (savedToken && savedUser) {
        token.value = savedToken;
        user.value = JSON.parse(savedUser);

        // 验证token是否仍然有效
        await verifyToken();
      }
    } catch (error) {
      console.warn("用户状态初始化失败:", error);
      clearAuth();
    }
  };

  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const login = async (credentials) => {
    try {
      loading.value = true;

      const response = await authAPI.login(credentials);

      if (response.success) {
        // 设置用户信息和token
        setUser(response.data.user);
        setToken(response.data.token);

        // 获取用户权限
        await fetchUserPermissions();

        ElMessage.success(response.message || "登录成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("登录失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 用户登出
   * @param {boolean} showMessage - 是否显示登出消息
   */
  const logout = async (showMessage = true) => {
    try {
      loading.value = true;

      // 调用后端登出接口
      if (token.value) {
        try {
          await authAPI.logout();
        } catch (error) {
          console.warn("后端登出失败:", error);
        }
      }

      // 清除本地状态
      clearAuth();

      if (showMessage) {
        ElMessage.success("已安全退出");
      }
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 验证token有效性
   */
  const verifyToken = async () => {
    try {
      if (!token.value) return false;

      const response = await authAPI.verify();

      if (response.success) {
        // 更新用户信息
        if (response.data.user) {
          setUser(response.data.user);
        }
        return true;
      } else {
        clearAuth();
        return false;
      }
    } catch (error) {
      console.warn("Token验证失败:", error);
      clearAuth();
      return false;
    }
  };

  /**
   * 获取用户权限
   */
  const fetchUserPermissions = async () => {
    try {
      if (!user.value?.id) return;

      const response = await authAPI.getUserPermissions(user.value.id);

      if (response.success) {
        permissions.value = response.data.permissions || [];
      }
    } catch (error) {
      console.warn("获取用户权限失败:", error);
      permissions.value = [];
    }
  };

  /**
   * 获取用户详细信息
   */
  const fetchUserProfile = async () => {
    try {
      if (!user.value?.id) return;

      loading.value = true;
      const response = await authAPI.getUserProfile(user.value.id);

      if (response.success) {
        setUser(response.data.user);
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新用户信息
   * @param {Object} userData - 用户数据
   */
  const updateUserProfile = async (userData) => {
    try {
      loading.value = true;

      const response = await authAPI.updateProfile(userData);

      if (response.success) {
        setUser(response.data.user);
        ElMessage.success("个人信息更新成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("更新用户信息失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   */
  const changePassword = async (passwordData) => {
    try {
      loading.value = true;

      const response = await authAPI.changePassword(passwordData);

      if (response.success) {
        ElMessage.success("密码修改成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("修改密码失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  // ===== 辅助方法 =====

  /**
   * 设置用户信息
   * @param {Object} userData - 用户数据
   */
  const setUser = (userData) => {
    user.value = userData;
    lastLoginTime.value = new Date().toISOString();

    // 同步到localStorage
    if (userData) {
      localStorage.setItem("user", JSON.stringify(userData));
    } else {
      localStorage.removeItem("user");
    }
  };

  /**
   * 更新用户头像
   * @param {string} avatarPath - 头像路径
   */
  const updateAvatar = (avatarPath) => {
    if (user.value) {
      user.value.avatar = avatarPath;
      // 同步到localStorage
      localStorage.setItem("user", JSON.stringify(user.value));
    }
  };

  /**
   * 设置token
   * @param {string} tokenValue - token值
   */
  const setToken = (tokenValue) => {
    token.value = tokenValue;

    // 同步到localStorage
    if (tokenValue) {
      localStorage.setItem("token", tokenValue);
    } else {
      localStorage.removeItem("token");
    }
  };

  /**
   * 清除认证信息
   */
  const clearAuth = () => {
    user.value = null;
    token.value = null;
    permissions.value = [];
    lastLoginTime.value = null;

    // 清除localStorage
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  };

  // 返回store的公共接口
  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    permissions: readonly(permissions),
    lastLoginTime: readonly(lastLoginTime),

    // 计算属性
    isAuthenticated,
    userRole,
    userName,
    userDisplayName,
    isAdmin,
    isReviewer,
    isEmployee,
    hasPermission,
    canAccessRoute,

    // 操作方法
    initAuth,
    login,
    logout,
    verifyToken,
    fetchUserPermissions,
    fetchUserProfile,
    updateUserProfile,
    changePassword,
    setUser,
    setToken,
    updateAvatar,
    clearAuth,
  };
});
