/**
 * 数据库操作工具类
 * 提供 SQLite 数据库的基础操作方法
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

class Database {
  constructor() {
    this.db = null;
    this.connect();
  }

  // 连接数据库
  connect() {
    this.db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('数据库连接失败:', err.message);
        throw err;
      }
      console.log('✅ 数据库连接成功');

      // 启用外键约束
      this.db.run('PRAGMA foreign_keys = ON');
    });
  }

  // 执行查询 - 返回单条记录
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('数据库查询错误:', err.message);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 执行查询 - 返回多条记录
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('数据库查询错误:', err.message);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 执行 SQL - 增删改操作
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) {
          console.error('数据库执行错误:', err.message);
          reject(err);
        } else {
          resolve({
            id: this.lastID,
            changes: this.changes
          });
        }
      });
    });
  }

  // 执行事务
  async transaction(operations) {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');

        Promise.all(operations.map(op => this.run(op.sql, op.params)))
          .then(results => {
            this.db.run('COMMIT', (err) => {
              if (err) {
                reject(err);
              } else {
                resolve(results);
              }
            });
          })
          .catch(error => {
            this.db.run('ROLLBACK');
            reject(error);
          });
      });
    });
  }

  // 分页查询
  async paginate(sql, params = [], page = 1, pageSize = 10) {
    // 计算总数
    const countSql = sql.replace(/SELECT .+ FROM/, 'SELECT COUNT(*) as total FROM');
    const countResult = await this.get(countSql, params);
    const total = countResult ? (countResult.total || countResult['COUNT(*)'] || 0) : 0;

    // 分页查询
    const offset = (page - 1) * pageSize;
    const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
    const rows = await this.all(paginatedSql, [...params, pageSize, offset]);

    return {
      data: rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('关闭数据库连接失败:', err.message);
        } else {
          console.log('✅ 数据库连接已关闭');
        }
      });
    }
  }
}

// 创建数据库实例
const database = new Database();

// 用户相关操作
const UserModel = {
  // 根据用户名查找用户
  async findByUsername(username) {
    const sql = 'SELECT * FROM users WHERE username = ?';
    return await database.get(sql, [username]);
  },

  // 根据ID查找用户 (支持RBAC)
  async findById(id) {
    const sql = `
      SELECT u.id, u.username, u.role, u.status, u.created_at, u.updated_at, u.created_by, u.avatar,
             r.name as role_name, r.display_name as role_display_name, r.level as role_level
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `;
    return await database.get(sql, [id]);
  },

  // 创建用户
  async create(userData) {
    const { username, password, role, status = 'active', created_by } = userData;
    const sql = `
      INSERT INTO users (username, password, role, status, created_by)
      VALUES (?, ?, ?, ?, ?)
    `;
    return await database.run(sql, [username, password, role, status, created_by]);
  },

  // 更新用户
  async update(id, userData) {
    const fields = [];
    const values = [];

    Object.keys(userData).forEach(key => {
      if (['username', 'password', 'role', 'status', 'real_name', 'email', 'phone', 'avatar'].includes(key)) {
        fields.push(`${key} = ?`);
        values.push(userData[key]);
      }
    });

    if (fields.length === 0) return { changes: 0 };

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
    return await database.run(sql, values);
  },

  // 删除用户
  async delete(id) {
    const sql = 'DELETE FROM users WHERE id = ?';
    return await database.run(sql, [id]);
  },

  // 更新用户头像
  async updateAvatar(id, avatarPath) {
    const sql = 'UPDATE users SET avatar = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    return await database.run(sql, [avatarPath, id]);
  },

  // 获取用户列表 (支持RBAC)
  async getList(filters = {}, page = 1, pageSize = 10) {
    let sql = `
      SELECT u.id, u.username, u.role, u.status, u.created_at, u.updated_at, u.created_by, u.avatar,
             r.name as role_name, r.display_name as role_display_name, r.level as role_level
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE 1=1
    `;
    const params = [];

    if (filters.role) {
      if (Array.isArray(filters.role)) {
        // 处理角色数组 - 支持新旧角色名称
        const placeholders = filters.role.map(() => '?').join(',');
        sql += ` AND (u.role IN (${placeholders}) OR r.name IN (${placeholders}))`;
        params.push(...filters.role, ...filters.role);
      } else {
        // 处理单个角色 - 支持新旧角色名称
        sql += ' AND (u.role = ? OR r.name = ?)';
        params.push(filters.role, filters.role);
      }
    }

    if (filters.status) {
      sql += ' AND u.status = ?';
      params.push(filters.status);
    }

    if (filters.keyword) {
      sql += ' AND u.username LIKE ?';
      params.push(`%${filters.keyword}%`);
    }

    sql += ' ORDER BY u.created_at DESC';

    return await database.paginate(sql, params, page, pageSize);
  },

  // 软删除用户
  async softDelete(id) {
    const sql = 'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    return await database.run(sql, ['banned', id]);
  },

  // 高级搜索
  async advancedSearch(searchParams) {
    const {
      keyword,
      role,
      status,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = searchParams;

    let whereConditions = [];
    let params = [];

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(username LIKE ? OR real_name LIKE ? OR email LIKE ?)');
      const keywordParam = `%${keyword}%`;
      params.push(keywordParam, keywordParam, keywordParam);
    }

    // 角色筛选
    if (role) {
      whereConditions.push('role = ?');
      params.push(role);
    }

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      params.push(status);
    }

    // 日期范围筛选
    if (startDate) {
      whereConditions.push('created_at >= ?');
      params.push(startDate);
    }

    if (endDate) {
      whereConditions.push('created_at <= ?');
      params.push(endDate);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM users
      ${whereClause}
    `;

    const countResult = await database.get(countSql, params);
    const total = countResult.total;

    // 查询列表
    const offset = (page - 1) * limit;
    const listSql = `
      SELECT id, username, role, status, created_at, updated_at, real_name, email, phone, avatar
      FROM users
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder.toUpperCase()}
      LIMIT ? OFFSET ?
    `;

    const listParams = [...params, limit, offset];
    const users = await database.all(listSql, listParams);

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  },

  // 获取搜索建议
  async getSearchSuggestions(keyword) {
    const sql = `
      SELECT DISTINCT username, real_name, email
      FROM users
      WHERE (username LIKE ? OR real_name LIKE ? OR email LIKE ?)
      AND status = 'active'
      ORDER BY created_at DESC
      LIMIT 10
    `;

    const keywordParam = `%${keyword}%`;
    const results = await database.all(sql, [keywordParam, keywordParam, keywordParam]);

    // 提取建议
    const suggestions = [];
    results.forEach(row => {
      if (row.username.toLowerCase().includes(keyword.toLowerCase())) {
        suggestions.push({ type: 'username', value: row.username });
      }
      if (row.real_name && row.real_name.toLowerCase().includes(keyword.toLowerCase())) {
        suggestions.push({ type: 'real_name', value: row.real_name });
      }
      if (row.email && row.email.toLowerCase().includes(keyword.toLowerCase())) {
        suggestions.push({ type: 'email', value: row.email });
      }
    });

    return suggestions.slice(0, 10);
  },

  // 获取角色统计信息
  async getRoleStats() {
    const sql = `
      SELECT role, COUNT(*) as count
      FROM users
      WHERE status = 'active'
      GROUP BY role
      ORDER BY count DESC
    `;

    const results = await database.all(sql);

    // 转换为对象格式
    const stats = {};
    results.forEach(row => {
      stats[row.role] = row.count;
    });

    return {
      total: results.reduce((sum, row) => sum + row.count, 0),
      byRole: stats,
      details: results
    };
  },

  // 获取活跃用户列表
  async getActiveUsers(limit = 50) {
    const sql = `
      SELECT id, username, role, created_at
      FROM users
      WHERE status = 'active'
      ORDER BY created_at DESC
      LIMIT ?
    `;

    return await database.all(sql, [limit]);
  },

  // 根据角色查找用户
  async findByRole(role) {
    const sql = `
      SELECT id, username, role, status, created_at
      FROM users
      WHERE role = ? AND status = 'active'
      ORDER BY created_at ASC
    `;
    return await database.all(sql, [role]);
  }
};

// 合同相关操作
const ContractModel = {
  // 创建合同
  async create(contractData) {
    const {
      serial_number, submitter_id, reviewer_id, filename,
      file_path, file_size, submit_note, review_level
    } = contractData;

    const sql = `
      INSERT INTO contracts (
        serial_number, submitter_id, reviewer_id, filename,
        file_path, file_size, submit_note, review_level, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
    `;

    return await database.run(sql, [
      serial_number, submitter_id, reviewer_id, filename,
      file_path, file_size, submit_note, review_level
    ]);
  },

  // 根据ID查找合同
  async findById(id) {
    const sql = `
      SELECT c.*, 
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      WHERE c.id = ?
    `;
    return await database.get(sql, [id]);
  },

  // 根据流水号查找合同
  async findBySerialNumber(serialNumber) {
    const sql = `
      SELECT c.*, 
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      WHERE c.serial_number = ?
    `;
    return await database.get(sql, [serialNumber]);
  },

  // 根据文件名查找合同
  async findByFilename(filename) {
    const sql = `
      SELECT c.*, 
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      WHERE c.filename = ?
    `;
    return await database.get(sql, [filename]);
  },

  // 更新合同
  async update(id, contractData) {
    const fields = [];
    const values = [];

    Object.keys(contractData).forEach(key => {
      if (['filename', 'file_path', 'file_size', 'status', 'review_comment', 'reviewed_at', 'reviewer_id', 'submit_note'].includes(key)) {
        fields.push(`${key} = ?`);
        values.push(contractData[key]);
      }
    });

    if (fields.length === 0) return { changes: 0 };

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const sql = `UPDATE contracts SET ${fields.join(', ')} WHERE id = ?`;
    return await database.run(sql, values);
  },

  // 删除合同
  async delete(id) {
    const sql = 'DELETE FROM contracts WHERE id = ?';
    return await database.run(sql, [id]);
  },

  // 获取合同列表
  async getList(filters = {}, page = 1, pageSize = 10) {
    let sql = `
      SELECT c.*, 
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      WHERE 1=1
    `;
    const params = [];

    if (filters.submitter_id) {
      sql += ' AND c.submitter_id = ?';
      params.push(filters.submitter_id);
    }

    if (filters.reviewer_id) {
      sql += ' AND c.reviewer_id = ?';
      params.push(filters.reviewer_id);
    }

    if (filters.status) {
      if (Array.isArray(filters.status)) {
        // 处理状态数组
        const placeholders = filters.status.map(() => '?').join(',');
        sql += ` AND c.status IN (${placeholders})`;
        params.push(...filters.status);
      } else {
        // 处理单个状态
        sql += ' AND c.status = ?';
        params.push(filters.status);
      }
    }

    if (filters.status_not) {
      sql += ' AND c.status != ?';
      params.push(filters.status_not);
    }

    sql += ' ORDER BY c.created_at DESC';

    return await database.paginate(sql, params, page, pageSize);
  },

  // 生成流水号
  async generateSerialNumber() {
    // 查询现有的最大编号
    const result = await database.get(`
      SELECT serial_number
      FROM contracts
      WHERE serial_number LIKE 'HT%'
      ORDER BY CAST(SUBSTR(serial_number, 3) AS INTEGER) DESC
      LIMIT 1
    `);

    let nextNumber = 1; // 默认从 1 开始

    if (result && result.serial_number) {
      // 提取现有最大编号中的数字部分
      const currentNumber = parseInt(result.serial_number.substring(2));
      nextNumber = currentNumber + 1;
    }

    return `HT${String(nextNumber).padStart(3, '0')}`;
  },

  // 获取统计数据
  async getStats(filters = {}) {
    let sql = 'SELECT status, COUNT(*) as count FROM contracts WHERE 1=1';
    const params = [];

    if (filters.submitter_id) {
      sql += ' AND submitter_id = ?';
      params.push(filters.submitter_id);
    }

    if (filters.reviewer_id) {
      sql += ' AND reviewer_id = ?';
      params.push(filters.reviewer_id);
    }

    if (filters.status_not) {
      sql += ' AND status != ?';
      params.push(filters.status_not);
    }

    sql += ' GROUP BY status';

    const rows = await database.all(sql, params);

    // 转换为对象格式
    const stats = {
      pending: 0,
      approved: 0,
      rejected: 0,
      total: 0
    };

    rows.forEach(row => {
      stats[row.status] = row.count;
      stats.total += row.count;
    });

    return stats;
  },

  // 高级搜索
  async advancedSearch(searchParams) {
    const {
      keyword,
      status,
      submitterId,
      reviewerId,
      startDate,
      endDate,
      minSize,
      maxSize,
      fileType,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20,
      currentUserId,
      userRole
    } = searchParams;

    let whereConditions = [];
    let params = [];

    // 权限控制
    if (userRole === 'employee') {
      whereConditions.push('c.submitter_id = ?');
      params.push(currentUserId);
    } else if (userRole === 'reviewer') {
      whereConditions.push('c.reviewer_id = ?');
      params.push(currentUserId);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(c.serial_number LIKE ? OR c.filename LIKE ? OR c.submit_note LIKE ? OR c.review_comment LIKE ?)');
      const keywordParam = `%${keyword}%`;
      params.push(keywordParam, keywordParam, keywordParam, keywordParam);
    }

    // 状态筛选
    if (status) {
      whereConditions.push('c.status = ?');
      params.push(status);
    }

    // 提交人筛选
    if (submitterId) {
      whereConditions.push('c.submitter_id = ?');
      params.push(submitterId);
    }

    // 审核人筛选
    if (reviewerId) {
      whereConditions.push('c.reviewer_id = ?');
      params.push(reviewerId);
    }

    // 日期范围筛选
    if (startDate) {
      whereConditions.push('c.created_at >= ?');
      params.push(startDate);
    }

    if (endDate) {
      whereConditions.push('c.created_at <= ?');
      params.push(endDate);
    }

    // 文件大小筛选
    if (minSize !== undefined) {
      whereConditions.push('c.file_size >= ?');
      params.push(minSize);
    }

    if (maxSize !== undefined) {
      whereConditions.push('c.file_size <= ?');
      params.push(maxSize);
    }

    // 文件类型筛选
    if (fileType) {
      whereConditions.push('c.filename LIKE ?');
      params.push(`%.${fileType}`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM contracts c
      ${whereClause}
    `;

    const countResult = await database.get(countSql, params);
    const total = countResult.total;

    // 查询列表
    const offset = (page - 1) * limit;
    const listSql = `
      SELECT c.*, 
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      ${whereClause}
      ORDER BY c.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT ? OFFSET ?
    `;

    const listParams = [...params, limit, offset];
    const contracts = await database.all(listSql, listParams);

    return {
      contracts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  },

  // 获取搜索建议
  async getSearchSuggestions(keyword, options = {}) {
    const { currentUserId, userRole } = options;

    let whereConditions = [];
    let params = [];

    // 权限控制
    if (userRole === 'employee') {
      whereConditions.push('c.submitter_id = ?');
      params.push(currentUserId);
    } else if (userRole === 'reviewer') {
      whereConditions.push('c.reviewer_id = ?');
      params.push(currentUserId);
    }

    // 关键词匹配
    whereConditions.push('(c.serial_number LIKE ? OR c.filename LIKE ?)');
    const keywordParam = `%${keyword}%`;
    params.push(keywordParam, keywordParam);

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const sql = `
      SELECT DISTINCT c.serial_number, c.filename
      FROM contracts c
      ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT 10
    `;

    const results = await database.all(sql, params);

    // 提取建议
    const suggestions = [];
    results.forEach(row => {
      if (row.serial_number.toLowerCase().includes(keyword.toLowerCase())) {
        suggestions.push({ type: 'serial_number', value: row.serial_number });
      }
      if (row.filename.toLowerCase().includes(keyword.toLowerCase())) {
        suggestions.push({ type: 'filename', value: row.filename });
      }
    });

    return suggestions.slice(0, 10);
  },

  // 统计相关方法
  async getTotalCount() {
    const sql = 'SELECT COUNT(*) as count FROM contracts';
    const result = await database.get(sql);
    return result.count;
  },

  async getCountByStatus() {
    const sql = 'SELECT status, COUNT(*) as count FROM contracts GROUP BY status';
    const results = await database.all(sql);

    const stats = {};
    results.forEach(row => {
      stats[row.status] = row.count;
    });

    return stats;
  },

  async getCountByMonth() {
    const sql = `
      SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
      FROM contracts
      WHERE created_at >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month DESC
    `;

    return await database.all(sql);
  },

  async getTopSubmitters() {
    const sql = `
      SELECT u.username, COUNT(c.id) as count
      FROM contracts c
      LEFT JOIN users u ON c.submitter_id = u.id
      GROUP BY c.submitter_id
      ORDER BY count DESC
      LIMIT 10
    `;

    return await database.all(sql);
  },

  async getTopReviewers() {
    const sql = `
      SELECT u.username, COUNT(c.id) as count
      FROM contracts c
      LEFT JOIN users u ON c.reviewer_id = u.id
      WHERE c.reviewer_id IS NOT NULL
      GROUP BY c.reviewer_id
      ORDER BY count DESC
      LIMIT 10
    `;

    return await database.all(sql);
  },

  async getTotalCountBySubmitter(submitterId) {
    const sql = 'SELECT COUNT(*) as count FROM contracts WHERE submitter_id = ?';
    const result = await database.get(sql, [submitterId]);
    return result.count;
  },

  async getTotalCountByReviewer(reviewerId) {
    const sql = 'SELECT COUNT(*) as count FROM contracts WHERE reviewer_id = ?';
    const result = await database.get(sql, [reviewerId]);
    return result.count;
  },

  async getCountByStatusAndSubmitter(submitterId) {
    const sql = 'SELECT status, COUNT(*) as count FROM contracts WHERE submitter_id = ? GROUP BY status';
    const results = await database.all(sql, [submitterId]);

    const stats = {};
    results.forEach(row => {
      stats[row.status] = row.count;
    });

    return stats;
  },

  async getCountByStatusAndReviewer(reviewerId) {
    const sql = 'SELECT status, COUNT(*) as count FROM contracts WHERE reviewer_id = ? GROUP BY status';
    const results = await database.all(sql, [reviewerId]);

    const stats = {};
    results.forEach(row => {
      stats[row.status] = row.count;
    });

    return stats;
  },

  async getCountByMonthAndSubmitter(submitterId) {
    const sql = `
      SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
      FROM contracts
      WHERE submitter_id = ? AND created_at >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month DESC
    `;

    return await database.all(sql, [submitterId]);
  },

  async getCountByMonthAndReviewer(reviewerId) {
    const sql = `
      SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
      FROM contracts
      WHERE reviewer_id = ? AND created_at >= date('now', '-12 months')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month DESC
    `;

    return await database.all(sql, [reviewerId]);
  },

  // 获取状态统计信息（用于缓存预热）
  async getStatusStats() {
    const sql = `
      SELECT status, COUNT(*) as count
      FROM contracts
      GROUP BY status
      ORDER BY count DESC
    `;

    const results = await database.all(sql);

    // 转换为对象格式
    const stats = {};
    results.forEach(row => {
      stats[row.status] = row.count;
    });

    return {
      total: results.reduce((sum, row) => sum + row.count, 0),
      byStatus: stats,
      details: results
    };
  },

  // 获取最近合同列表（用于缓存预热）
  async getRecent(limit = 20) {
    const sql = `
      SELECT c.*,
             s.username as submitter_name,
             r.username as reviewer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users r ON c.reviewer_id = r.id
      ORDER BY c.created_at DESC
      LIMIT ?
    `;

    return await database.all(sql, [limit]);
  }
};

// 通知相关操作
const NotificationModel = {
  // 创建通知
  async create(notificationData) {
    const {
      user_id, type, title, content, related_id, related_type
    } = notificationData;

    const sql = `
      INSERT INTO notifications (
        user_id, type, title, content, related_id, related_type
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    return await database.run(sql, [
      user_id, type, title, content, related_id, related_type
    ]);
  },

  // 根据ID查找通知
  async findById(id) {
    const sql = 'SELECT * FROM notifications WHERE id = ?';
    return await database.get(sql, [id]);
  },

  // 获取用户通知列表
  async getByUserId(userId, filters = {}, page = 1, pageSize = 20) {
    let sql = `
      SELECT * FROM notifications
      WHERE user_id = ?
    `;
    const params = [userId];

    // 类型筛选
    if (filters.type) {
      sql += ' AND type = ?';
      params.push(filters.type);
    }

    // 已读状态筛选
    if (filters.is_read !== undefined) {
      sql += ' AND is_read = ?';
      params.push(filters.is_read ? 1 : 0);
    }

    sql += ' ORDER BY created_at DESC';

    return await database.paginate(sql, params, page, pageSize);
  },

  // 标记通知为已读
  async markAsRead(id, userId) {
    const sql = `
      UPDATE notifications
      SET is_read = 1, read_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `;
    return await database.run(sql, [id, userId]);
  },

  // 批量标记为已读
  async markAllAsRead(userId) {
    const sql = `
      UPDATE notifications
      SET is_read = 1, read_at = CURRENT_TIMESTAMP
      WHERE user_id = ? AND is_read = 0
    `;
    return await database.run(sql, [userId]);
  },

  // 删除通知
  async delete(id, userId) {
    const sql = 'DELETE FROM notifications WHERE id = ? AND user_id = ?';
    return await database.run(sql, [id, userId]);
  },

  // 获取未读通知数量
  async getUnreadCount(userId) {
    const sql = 'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0';
    const result = await database.get(sql, [userId]);
    return result.count;
  },

  // 获取通知统计
  async getStats(userId) {
    const sql = `
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read
      FROM notifications
      WHERE user_id = ?
    `;
    return await database.get(sql, [userId]);
  },

  // 清理过期通知（30天前的已读通知）
  async cleanupOldNotifications() {
    const sql = `
      DELETE FROM notifications
      WHERE is_read = 1
      AND read_at < datetime('now', '-30 days')
    `;
    return await database.run(sql);
  }
};

module.exports = {
  database,
  UserModel,
  ContractModel,
  NotificationModel
};
