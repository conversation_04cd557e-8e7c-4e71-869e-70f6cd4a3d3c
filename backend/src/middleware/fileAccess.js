/**
 * 文件访问权限控制中间件
 * 提供基于用户权限和合同关系的文件访问控制
 */

const path = require('path');
const { ContractModel } = require('../utils/database');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, USER_ROLES } = require('../utils/constants');
const { UserPermissionModel } = require('../models/rbac');
const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

/**
 * 获取客户端IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null);
}

/**
 * 验证文件访问权限
 * 只有与合同相关的用户才能访问文件
 */
const validateFileAccess = async (req, res, next) => {
  try {
    const filename = req.params.filename;
    const user = req.user;

    if (!user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 基本的文件名验证
    if (!filename || typeof filename !== 'string') {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('无效的文件名', HTTP_STATUS.BAD_REQUEST)
      );
    }

    // 防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      await logFileAccessAttempt(req, filename, 'path_traversal_blocked');
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('非法的文件访问', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 查找与文件名关联的合同
    const contract = await ContractModel.findByFilename(filename);

    if (!contract) {
      await logFileAccessAttempt(req, filename, 'file_not_found');
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('文件未找到', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 权限检查
    const hasAccess = await checkFileAccessPermission(user, contract);

    if (!hasAccess) {
      await logFileAccessAttempt(req, filename, 'access_denied');
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('您没有权限访问此文件', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 记录成功的文件访问日志
    await logFileAccessAttempt(req, filename, 'access_granted');

    // 将合同信息附加到请求对象
    req.contract = contract;
    req.filename = filename;

    next();
  } catch (error) {
    console.error('文件访问权限验证错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('权限验证失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 检查文件访问权限 (使用RBAC权限系统)
 * @param {Object} user - 用户信息
 * @param {Object} contract - 合同信息
 * @returns {Promise<boolean>} 是否有权限访问
 */
async function checkFileAccessPermission(user, contract) {
  try {
    // 获取用户的RBAC角色信息
    const userRole = await UserPermissionModel.getUserRole(user.id);

    // 管理员可以访问所有文件
    if (userRole && userRole.name === 'admin') {
      return true;
    }

    // 合同提交人可以访问自己的文件
    if (contract.submitter_id === user.id) {
      return true;
    }

    // 检查用户是否有合同查看权限
    const hasReadPermission = await UserPermissionModel.userHasPermission(user.id, 'contract:read');
    if (!hasReadPermission) {
      return false;
    }

    // 指定的审核人可以访问文件
    if (contract.reviewer_id === user.id) {
      // 检查是否是审核员角色
      if (userRole && (userRole.name === 'county_reviewer' || userRole.name === 'city_reviewer' || userRole.name === 'reviewer')) {
        return true;
      }
      // 兼容旧的reviewer角色
      if ([USER_ROLES.REVIEWER, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(user.role)) {
        return true;
      }
    }

    // 如果是审核员但不是指定审核人，则不能访问
    if (userRole && (userRole.name === 'county_reviewer' || userRole.name === 'city_reviewer') && contract.reviewer_id !== user.id) {
      return false;
    }

    // 兼容旧角色系统：如果是审核员但不是指定审核人，则不能访问
    if (user.role === USER_ROLES.REVIEWER && contract.reviewer_id !== user.id) {
      return false;
    }

    // 其他情况拒绝访问
    return false;
  } catch (error) {
    console.error('检查文件访问权限错误:', error);
    return false;
  }
}

/**
 * 记录文件访问尝试
 * @param {Object} req - 请求对象
 * @param {string} filename - 文件名
 * @param {string} result - 访问结果
 */
async function logFileAccessAttempt(req, filename, result) {
  try {
    const action = req.method === 'GET' ?
      (req.path.includes('preview') ? 'preview_file' : 'download_file') :
      'access_file';

    const status = result === 'access_granted' ? 'success' : 'failed';

    await logOperation({
      userId: req.user?.id || 0,
      action,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: filename,
      details: JSON.stringify({
        filename,
        result,
        method: req.method,
        url: req.path,
        userAgent: req.headers['user-agent']
      }),
      ipAddress: getClientIP(req),
      userAgent: req.headers['user-agent'],
      status
    });
  } catch (error) {
    console.error('文件访问日志记录失败:', error);
  }
}

/**
 * 文件下载权限检查
 * 更严格的下载权限控制
 */
const validateDownloadPermission = async (req, res, next) => {
  try {
    const contract = req.contract;
    const user = req.user;

    if (!contract) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('合同不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 检查合同状态，某些状态下可能限制下载
    const restrictedStatuses = []; // 可以根据需要添加限制状态

    if (restrictedStatuses.includes(contract.status)) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('当前状态下不允许下载文件', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 检查文件下载次数限制（可选）
    // const downloadCount = await getFileDownloadCount(contract.id, user.id);
    // if (downloadCount > MAX_DOWNLOAD_LIMIT) {
    //   return res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
    //     ResponseUtils.error('下载次数超过限制', HTTP_STATUS.TOO_MANY_REQUESTS)
    //   );
    // }

    next();
  } catch (error) {
    console.error('文件下载权限检查错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('权限检查失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 文件预览权限检查
 * 相对宽松的预览权限
 */
const validatePreviewPermission = async (req, res, next) => {
  try {
    const contract = req.contract;
    const user = req.user;

    if (!contract) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('合同不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 预览权限相对宽松，基本权限验证通过即可
    next();
  } catch (error) {
    console.error('文件预览权限检查错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('权限检查失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 文件删除权限检查
 * 只有管理员或合同提交人在特定状态下才能删除
 */
const validateDeletePermission = async (req, res, next) => {
  try {
    const contract = req.contract;
    const user = req.user;

    if (!contract) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('合同不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 管理员可以删除任何文件
    if (user.role === USER_ROLES.ADMIN) {
      return next();
    }

    // 只有合同提交人才能删除自己的文件
    if (contract.submitter_id !== user.id) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('您没有权限删除此文件', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 只有在待审核或被拒绝状态下才能删除
    const allowedStatuses = ['pending', 'rejected'];
    if (!allowedStatuses.includes(contract.status)) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('当前状态下不允许删除文件', HTTP_STATUS.FORBIDDEN)
      );
    }

    next();
  } catch (error) {
    console.error('文件删除权限检查错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('权限检查失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 文件访问频率限制
 * 防止文件下载滥用
 */
const fileAccessRateLimit = (() => {
  const accessCounts = new Map();
  const TIME_WINDOW = 60 * 1000; // 1分钟
  const MAX_REQUESTS = 30; // 每分钟最多30次请求

  return (req, res, next) => {
    const key = `${req.user.id}-${req.ip}`;
    const now = Date.now();

    // 清理过期记录
    for (const [recordKey, record] of accessCounts.entries()) {
      if (now - record.timestamp > TIME_WINDOW) {
        accessCounts.delete(recordKey);
      }
    }

    // 检查当前用户的访问次数
    const userAccess = accessCounts.get(key);
    if (userAccess) {
      if (now - userAccess.timestamp < TIME_WINDOW) {
        if (userAccess.count >= MAX_REQUESTS) {
          return res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
            ResponseUtils.error('访问过于频繁，请稍后再试', HTTP_STATUS.TOO_MANY_REQUESTS)
          );
        }
        userAccess.count++;
      } else {
        userAccess.count = 1;
        userAccess.timestamp = now;
      }
    } else {
      accessCounts.set(key, { count: 1, timestamp: now });
    }

    next();
  };
})();

/**
 * 基于合同ID验证文件访问权限 (任务要求的优化版本)
 * 用于 GET /api/files/:id/preview 和 GET /api/files/:id/download 端点
 */
const validateContractFileAccess = async (req, res, next) => {
  try {
    const contractId = req.params.id;
    const user = req.user;

    if (!user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 验证合同ID
    if (!contractId || isNaN(parseInt(contractId))) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
      );
    }

    // 通过ID查找合同
    const contract = await ContractModel.findById(parseInt(contractId));

    if (!contract) {
      await logFileAccessAttempt(req, `contract_${contractId}`, 'contract_not_found');
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('合同不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 权限检查
    const hasAccess = await checkFileAccessPermission(user, contract);

    if (!hasAccess) {
      await logFileAccessAttempt(req, contract.filename, 'access_denied');
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('您没有权限访问此文件', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 记录成功的文件访问日志
    await logFileAccessAttempt(req, contract.filename, 'access_granted');

    // 将合同信息附加到请求对象
    req.contract = contract;

    next();
  } catch (error) {
    console.error('合同文件访问权限验证错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('权限验证失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 增强的权限检查函数 (使用RBAC权限系统)
 * 支持更细粒度的权限控制
 */
async function checkFileAccessPermissionEnhanced(user, contract) {
  try {
    // 获取用户的RBAC角色信息
    const userRole = await UserPermissionModel.getUserRole(user.id);

    // 管理员可以访问所有文件
    if (userRole && userRole.name === 'admin') {
      return true;
    }

    // 合同提交人可以访问自己的文件
    if (contract.submitter_id === user.id) {
      return true;
    }

    // 检查用户是否有合同查看权限
    const hasReadPermission = await UserPermissionModel.userHasPermission(user.id, 'contract:read');
    if (!hasReadPermission) {
      return false;
    }

    // 指定的审核人可以访问文件
    if (contract.reviewer_id === user.id) {
      // 检查是否是审核员角色
      if (userRole && (userRole.name === 'county_reviewer' || userRole.name === 'city_reviewer' || userRole.name === 'reviewer')) {
        return true;
      }
      // 兼容旧的reviewer角色
      if ([USER_ROLES.REVIEWER, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(user.role)) {
        return true;
      }
    }

    // 如果是审核员但不是指定审核人，检查是否有特殊权限
    if (userRole && (userRole.name === 'county_reviewer' || userRole.name === 'city_reviewer') && contract.reviewer_id !== user.id) {
      // 可以在这里添加更复杂的权限逻辑，比如：
      // - 部门级别的权限
      // - 临时授权
      // - 紧急访问权限
      return false;
    }

    // 兼容旧角色系统
    if (user.role === USER_ROLES.REVIEWER && contract.reviewer_id !== user.id) {
      return false;
    }

    // 其他情况拒绝访问
    return false;
  } catch (error) {
    console.error('增强权限检查错误:', error);
    return false;
  }
}

module.exports = {
  validateFileAccess,
  validateContractFileAccess,
  validateDownloadPermission,
  validatePreviewPermission,
  validateDeletePermission,
  fileAccessRateLimit,
  checkFileAccessPermissionEnhanced
};