/**
 * 添加测试合同数据脚本
 * 为县级和市级审核员创建测试合同
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
    db.close();
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH);
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
    db.close();
  });
}

// 主函数
async function addTestContracts() {
  try {
    console.log('🔄 开始添加测试合同数据...');

    // 获取审核员信息
    const reviewers = await allQuery(`
      SELECT id, username, role 
      FROM users 
      WHERE role IN ('county_reviewer', 'city_reviewer') 
      AND status = 'active'
      ORDER BY role, id
    `);

    console.log('📋 找到的审核员:', reviewers);

    // 获取员工信息（作为提交者）
    const employees = await allQuery(`
      SELECT id, username 
      FROM users 
      WHERE role = 'employee' 
      AND status = 'active'
      LIMIT 3
    `);

    console.log('📋 找到的员工:', employees);

    if (reviewers.length === 0 || employees.length === 0) {
      console.log('❌ 缺少必要的用户数据');
      return;
    }

    // 测试合同数据
    const testContracts = [
      // 县级审核员的合同
      {
        serial_number: 'HT901',
        submitter_id: employees[0].id,
        reviewer_id: reviewers.find(r => r.role === 'county_reviewer')?.id,
        filename: '县级测试合同001.pdf',
        file_path: 'test/county_contract_001.pdf',
        file_size: 1024000,
        status: 'pending',
        review_level: 'county_reviewer',
        submit_note: '县级审核测试合同，请审核'
      },
      {
        serial_number: 'HT902',
        submitter_id: employees[1] ? employees[1].id : employees[0].id,
        reviewer_id: reviewers.find(r => r.role === 'county_reviewer')?.id,
        filename: '县级测试合同002.pdf',
        file_path: 'test/county_contract_002.pdf',
        file_size: 2048000,
        status: 'pending',
        review_level: 'county_reviewer',
        submit_note: '县级审核测试合同2，待处理'
      },
      // 市级审核员的合同
      {
        serial_number: 'HT903',
        submitter_id: employees[0].id,
        reviewer_id: reviewers.find(r => r.role === 'city_reviewer')?.id,
        filename: '市级测试合同001.pdf',
        file_path: 'test/city_contract_001.pdf',
        file_size: 1536000,
        status: 'pending_city_review', // 市级审核使用正确的状态
        review_level: 'city_reviewer',
        submit_note: '市级审核测试合同，请审核'
      },
      {
        serial_number: 'HT904',
        submitter_id: employees[2] ? employees[2].id : employees[0].id,
        reviewer_id: reviewers.find(r => r.role === 'city_reviewer')?.id,
        filename: '市级测试合同002.pdf',
        file_path: 'test/city_contract_002.pdf',
        file_size: 3072000,
        status: 'pending_city_review', // 市级审核使用正确的状态
        review_level: 'city_reviewer',
        submit_note: '市级审核测试合同2，待处理'
      }
    ];

    // 插入测试合同
    for (const contract of testContracts) {
      if (!contract.reviewer_id) {
        console.log(`⚠️  跳过合同 ${contract.serial_number}，找不到对应的审核员`);
        continue;
      }

      try {
        const result = await runQuery(`
          INSERT OR IGNORE INTO contracts (
            serial_number, submitter_id, reviewer_id, filename, file_path, 
            file_size, status, review_level, submit_note, 
            submitted_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          contract.serial_number,
          contract.submitter_id,
          contract.reviewer_id,
          contract.filename,
          contract.file_path,
          contract.file_size,
          contract.status,
          contract.review_level,
          contract.submit_note
        ]);

        if (result.changes > 0) {
          console.log(`✅ 添加测试合同: ${contract.serial_number} (${contract.review_level})`);
        } else {
          console.log(`⚠️  合同 ${contract.serial_number} 已存在，跳过`);
        }
      } catch (error) {
        console.error(`❌ 添加合同 ${contract.serial_number} 失败:`, error.message);
      }
    }

    // 验证结果
    const countyContracts = await allQuery(`
      SELECT COUNT(*) as count
      FROM contracts
      WHERE review_level = 'county_reviewer' AND status = 'pending'
    `);

    const cityContracts = await allQuery(`
      SELECT COUNT(*) as count
      FROM contracts
      WHERE review_level = 'city_reviewer' AND status = 'pending_city_review'
    `);

    console.log('\n📊 测试数据统计:');
    console.log(`县级待审核合同: ${countyContracts[0].count} 个`);
    console.log(`市级待审核合同: ${cityContracts[0].count} 个`);

    console.log('🎉 测试合同数据添加完成！');

  } catch (error) {
    console.error('❌ 添加测试数据失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行添加操作
if (require.main === module) {
  addTestContracts();
}

module.exports = { addTestContracts };
