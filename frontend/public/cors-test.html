<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS 跨域测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #337ecc;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background: #f4f4f5;
            border: 1px solid #909399;
            color: #606266;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #67c23a;
            color: white;
        }
        .status.offline {
            background: #f56c6c;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CORS 跨域测试工具</h1>
        <p>当前页面地址: <code id="currentUrl"></code></p>
        <p>后端 API 地址: <code>http://localhost:3000/api</code></p>
        
        <div class="test-section">
            <h3>📊 服务状态检查</h3>
            <button onclick="checkServerStatus()">检查服务状态</button>
            <div id="serverStatus" class="result info">点击按钮检查服务状态...</div>
        </div>

        <div class="test-section">
            <h3>🌐 CORS 预检请求测试</h3>
            <button onclick="testCORSPreflight()">测试 OPTIONS 请求</button>
            <div id="corsResult" class="result info">点击按钮测试 CORS 预检...</div>
        </div>

        <div class="test-section">
            <h3>📡 API 请求测试</h3>
            <button onclick="testAPIRequest('GET', '/dashboard/stats')">GET 请求</button>
            <button onclick="testAPIRequest('POST', '/auth/login')">POST 请求</button>
            <div id="apiResult" class="result info">点击按钮测试 API 请求...</div>
        </div>

        <div class="test-section">
            <h3>🔄 Vite 代理测试</h3>
            <button onclick="testViteProxy()">测试代理转发</button>
            <div id="proxyResult" class="result info">点击按钮测试 Vite 代理...</div>
        </div>

        <div class="test-section">
            <h3>📋 网络信息</h3>
            <button onclick="showNetworkInfo()">显示网络信息</button>
            <div id="networkInfo" class="result info">点击按钮显示网络信息...</div>
        </div>
    </div>

    <script>
        // 显示当前页面 URL
        document.getElementById('currentUrl').textContent = window.location.href;

        // 检查服务器状态
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.textContent = '检查中...';
            statusDiv.className = 'result info';

            try {
                // 检查前端服务器（当前页面能访问说明前端正常）
                const frontendStatus = '✅ 前端服务器正常';

                // 检查后端服务器
                const backendResponse = await fetch('http://localhost:3000', { 
                    method: 'GET',
                    mode: 'cors'
                });
                const backendStatus = backendResponse.ok ? '✅ 后端服务器正常' : '❌ 后端服务器异常';

                // 检查 API 端点
                const apiResponse = await fetch('/api/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const apiStatus = '✅ API 端点可访问 (通过代理)';

                statusDiv.textContent = `${frontendStatus}\n${backendStatus}\n${apiStatus}`;
                statusDiv.className = 'result success';

            } catch (error) {
                statusDiv.textContent = `❌ 服务检查失败:\n${error.message}`;
                statusDiv.className = 'result error';
            }
        }

        // 测试 CORS 预检请求
        async function testCORSPreflight() {
            const resultDiv = document.getElementById('corsResult');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'result info';

            try {
                // 发送一个会触发预检的请求
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token'
                    },
                    body: JSON.stringify({ test: 'data' })
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                let result = '✅ CORS 预检请求成功\n\n📋 响应头信息:\n';
                for (const [key, value] of Object.entries(corsHeaders)) {
                    result += `${key}: ${value || '未设置'}\n`;
                }

                resultDiv.textContent = result;
                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent = `❌ CORS 预检失败:\n${error.message}\n\n这可能是跨域问题导致的。`;
                resultDiv.className = 'result error';
            }
        }

        // 测试 API 请求
        async function testAPIRequest(method, endpoint) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = `测试 ${method} ${endpoint}...`;
            resultDiv.className = 'result info';

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };

                if (method === 'POST') {
                    options.body = JSON.stringify({
                        username: '<EMAIL>',
                        password: 'test123456'
                    });
                }

                const response = await fetch(endpoint, options);
                const data = await response.json();

                let result = `✅ ${method} 请求成功\n\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}`;

                resultDiv.textContent = result;
                resultDiv.className = response.ok ? 'result success' : 'result error';

            } catch (error) {
                resultDiv.textContent = `❌ ${method} 请求失败:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试 Vite 代理
        async function testViteProxy() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.textContent = '测试代理转发...';
            resultDiv.className = 'result info';

            try {
                // 通过代理访问 API
                const proxyResponse = await fetch('/api/dashboard/stats');
                const proxyData = await proxyResponse.json();

                // 直接访问后端（会有跨域问题）
                let directResult = '';
                try {
                    const directResponse = await fetch('http://localhost:3000/api/dashboard/stats');
                    directResult = '✅ 直接访问后端成功（无跨域问题）';
                } catch (directError) {
                    directResult = '❌ 直接访问后端失败（跨域阻止）';
                }

                let result = `✅ Vite 代理转发成功\n\n`;
                result += `代理访问结果: ${JSON.stringify(proxyData, null, 2)}\n\n`;
                result += `${directResult}`;

                resultDiv.textContent = result;
                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent = `❌ 代理测试失败:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 显示网络信息
        function showNetworkInfo() {
            const infoDiv = document.getElementById('networkInfo');
            
            const info = {
                '当前域名': window.location.hostname,
                '当前端口': window.location.port,
                '协议': window.location.protocol,
                '完整URL': window.location.href,
                '用户代理': navigator.userAgent,
                '在线状态': navigator.onLine ? '在线' : '离线',
                '连接类型': navigator.connection ? navigator.connection.effectiveType : '未知'
            };

            let result = '📋 网络环境信息:\n\n';
            for (const [key, value] of Object.entries(info)) {
                result += `${key}: ${value}\n`;
            }

            infoDiv.textContent = result;
            infoDiv.className = 'result info';
        }

        // 页面加载时自动检查服务状态
        window.addEventListener('load', () => {
            setTimeout(checkServerStatus, 1000);
        });
    </script>
</body>
</html>
