#!/usr/bin/env node

/**
 * 数据库检查脚本
 * 用于诊断合同和文件数据的一致性问题
 */

// 使用后端的 node_modules
const sqlite3 = require('./backend/node_modules/sqlite3').verbose();
const fs = require('fs');
const path = require('path');

// 数据库路径 - 使用正确的数据库文件
const DB_PATH = path.join(__dirname, 'backend/database.sqlite');
const UPLOADS_PATH = path.join(__dirname, 'backend/uploads');

console.log('🔍 数据库和文件一致性检查');
console.log('================================');
console.log();

// 检查数据库文件是否存在
if (!fs.existsSync(DB_PATH)) {
    console.log('❌ 数据库文件不存在:', DB_PATH);
    process.exit(1);
}

// 检查上传目录是否存在
if (!fs.existsSync(UPLOADS_PATH)) {
    console.log('❌ 上传目录不存在:', UPLOADS_PATH);
    process.exit(1);
}

const db = new sqlite3.Database(DB_PATH);

// 检查合同表
function checkContracts() {
    return new Promise((resolve, reject) => {
        console.log('📋 1. 检查合同表数据');
        console.log('----------------------------------------');

        // 获取合同总数
        db.get('SELECT COUNT(*) as count FROM contracts', (err, row) => {
            if (err) {
                console.log('❌ 查询合同总数失败:', err.message);
                reject(err);
                return;
            }

            console.log(`合同总数: ${row.count}`);

            // 获取最近的10个合同
            db.all(`
                SELECT id, serial_number, filename, file_path, status, created_at 
                FROM contracts 
                ORDER BY id DESC 
                LIMIT 10
            `, (err, rows) => {
                if (err) {
                    console.log('❌ 查询合同列表失败:', err.message);
                    reject(err);
                    return;
                }

                console.log('\n最近的合同记录:');
                console.log('ID\t流水号\t\t文件名\t\t\t\t状态\t\t创建时间');
                console.log(''.padEnd(100, '-'));

                rows.forEach(row => {
                    const filename = (row.filename || '').substring(0, 30);
                    const filePath = (row.file_path || '').substring(0, 30);
                    console.log(`${row.id}\t${row.serial_number}\t${filename}\t${row.status}\t${row.created_at}`);

                    // 检查文件是否存在
                    const actualFile = row.file_path || row.filename;
                    if (actualFile) {
                        const fullPath = path.join(UPLOADS_PATH, actualFile);
                        const exists = fs.existsSync(fullPath);
                        console.log(`  文件存在: ${exists ? '✅' : '❌'} ${actualFile}`);
                    }
                });

                resolve(rows);
            });
        });
    });
}

// 检查特定ID的合同
function checkSpecificContract(contractId) {
    return new Promise((resolve, reject) => {
        console.log(`\n📋 2. 检查合同 ID ${contractId}`);
        console.log('----------------------------------------');

        db.get(`
            SELECT * FROM contracts WHERE id = ?
        `, [contractId], (err, row) => {
            if (err) {
                console.log('❌ 查询失败:', err.message);
                reject(err);
                return;
            }

            if (!row) {
                console.log(`❌ 合同 ID ${contractId} 不存在`);
                resolve(null);
                return;
            }

            console.log('合同信息:');
            console.log(`  ID: ${row.id}`);
            console.log(`  流水号: ${row.serial_number}`);
            console.log(`  文件名: ${row.filename}`);
            console.log(`  文件路径: ${row.file_path}`);
            console.log(`  状态: ${row.status}`);
            console.log(`  创建时间: ${row.created_at}`);
            console.log(`  提交者: ${row.submitter_id}`);

            // 检查物理文件
            const actualFile = row.file_path || row.filename;
            if (actualFile) {
                const fullPath = path.join(UPLOADS_PATH, actualFile);
                const exists = fs.existsSync(fullPath);
                console.log(`  物理文件: ${exists ? '✅ 存在' : '❌ 不存在'} - ${fullPath}`);

                if (exists) {
                    const stats = fs.statSync(fullPath);
                    console.log(`  文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
                    console.log(`  修改时间: ${stats.mtime}`);
                }
            } else {
                console.log('  ❌ 没有关联的文件');
            }

            resolve(row);
        });
    });
}

// 检查上传目录中的文件
function checkUploadedFiles() {
    return new Promise((resolve, reject) => {
        console.log('\n📋 3. 检查上传目录');
        console.log('----------------------------------------');

        try {
            const files = fs.readdirSync(UPLOADS_PATH);
            const pdfFiles = files.filter(file => file.endsWith('.pdf'));

            console.log(`上传目录中的 PDF 文件数量: ${pdfFiles.length}`);
            console.log('\nPDF 文件列表:');

            pdfFiles.forEach(file => {
                const fullPath = path.join(UPLOADS_PATH, file);
                const stats = fs.statSync(fullPath);
                console.log(`  ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
            });

            // 检查是否有孤立文件（数据库中没有记录的文件）
            console.log('\n检查孤立文件:');
            db.all('SELECT filename, file_path FROM contracts', (err, rows) => {
                if (err) {
                    console.log('❌ 查询数据库文件失败:', err.message);
                    reject(err);
                    return;
                }

                const dbFiles = new Set();
                rows.forEach(row => {
                    if (row.filename) dbFiles.add(row.filename);
                    if (row.file_path) dbFiles.add(row.file_path);
                });

                const orphanFiles = pdfFiles.filter(file => !dbFiles.has(file));

                if (orphanFiles.length > 0) {
                    console.log('❌ 发现孤立文件（数据库中没有记录）:');
                    orphanFiles.forEach(file => console.log(`  ${file}`));
                } else {
                    console.log('✅ 没有发现孤立文件');
                }

                resolve({ pdfFiles, orphanFiles });
            });

        } catch (error) {
            console.log('❌ 读取上传目录失败:', error.message);
            reject(error);
        }
    });
}

// 主函数
async function main() {
    try {
        // 检查合同表
        const contracts = await checkContracts();

        // 检查特定的合同 ID（从错误日志中看到的）
        await checkSpecificContract(45);
        await checkSpecificContract(55); // 从日志中看到的另一个ID

        // 检查上传目录
        await checkUploadedFiles();

        console.log('\n📋 4. 总结和建议');
        console.log('----------------------------------------');

        // 检查是否有合同但没有文件的情况
        db.all(`
            SELECT id, serial_number, filename, file_path 
            FROM contracts 
            WHERE filename IS NOT NULL OR file_path IS NOT NULL
        `, (err, rows) => {
            if (err) {
                console.log('❌ 最终检查失败:', err.message);
                db.close();
                return;
            }

            let missingFiles = 0;
            rows.forEach(row => {
                const actualFile = row.file_path || row.filename;
                if (actualFile) {
                    const fullPath = path.join(UPLOADS_PATH, actualFile);
                    if (!fs.existsSync(fullPath)) {
                        missingFiles++;
                    }
                }
            });

            console.log(`数据库中的合同记录: ${rows.length}`);
            console.log(`缺失物理文件的合同: ${missingFiles}`);

            if (missingFiles > 0) {
                console.log('\n🔧 建议解决方案:');
                console.log('1. 重新上传缺失的文件');
                console.log('2. 或者删除没有对应文件的合同记录');
                console.log('3. 检查本地和服务器的数据库是否需要同步');
            } else {
                console.log('\n✅ 数据库和文件系统一致性良好');
            }

            db.close();
            console.log('\n检查完成！');
        });

    } catch (error) {
        console.log('❌ 检查过程中发生错误:', error.message);
        db.close();
        process.exit(1);
    }
}

// 运行检查
main();
