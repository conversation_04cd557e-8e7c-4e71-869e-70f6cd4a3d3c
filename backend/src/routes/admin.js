/**
 * 管理员功能路由
 * 处理用户管理、系统管理等管理员功能
 */

const express = require('express');
const router = express.Router();
const { body, query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { RoleMiddleware, PermissionMiddleware } = require('../middleware/rbac');
const { responseCache, cacheInvalidation } = require('../middleware/cache');
const { database } = require('../utils/database');
const { HTTP_STATUS, USER_ROLES, RESPONSE_MESSAGES } = require('../utils/constants');
const { ResponseUtils } = require('../utils/helpers');
const { CacheHelpers } = require('../utils/cache');
const { UserModel } = require('../utils/database');
const bcrypt = require('bcrypt');

/**
 * 获取用户列表（管理员专用）
 * GET /api/admin/users
 */
router.get('/users',
  authenticateToken,
  RoleMiddleware.requireAdmin,
  responseCache({
    ttl: 300000, // 5分钟缓存
    type: 'admin',
    keyGenerator: (req) => {
      const params = new URLSearchParams(req.query).toString();
      return `admin_users_${params}`;
    },
    shouldCache: (req, res) => req.method === 'GET' && res.statusCode === 200
  }),
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('role')
      .optional()
      .isIn(Object.values(USER_ROLES))
      .withMessage('无效的用户角色'),
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'banned'])
      .withMessage('无效的用户状态'),
    query('keyword')
      .optional()
      .isLength({ max: 100 })
      .withMessage('搜索关键词不能超过100个字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { page = 1, pageSize = 10, role, status, keyword } = req.query;

      // 构建过滤条件
      const filters = {};
      if (role) filters.role = role;
      if (status) filters.status = status;
      if (keyword) filters.keyword = keyword;

      console.log('👥 管理员获取用户列表 - 用户信息:', {
        adminId: req.user.id,
        adminUsername: req.user.username,
        filters,
        pagination: { page, pageSize }
      });

      const result = await UserModel.getList(filters, parseInt(page), parseInt(pageSize));

      res.json(ResponseUtils.paginated(result.data, result.pagination));

    } catch (error) {
      console.error('管理员获取用户列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取系统统计信息
 * GET /api/admin/system-stats
 */
router.get('/system-stats',
  authenticateToken,
  PermissionMiddleware.canViewStats,
  async (req, res) => {
    try {
      // 获取用户统计
      const userStats = await database.all(`
        SELECT role, COUNT(*) as count
        FROM users
        WHERE status = 'active'
        GROUP BY role
      `);

      // 获取合同统计
      const contractStats = await database.all(`
        SELECT status, COUNT(*) as count
        FROM contracts
        GROUP BY status
      `);

      // 获取今日统计
      const today = new Date().toISOString().split('T')[0];
      const todayStats = await database.all(`
        SELECT
          COUNT(*) as total_today,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_today,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_today
        FROM contracts
        WHERE DATE(created_at) = ?
      `, [today]);

      // 获取文件统计
      const fileStats = await database.get(`
        SELECT
          COUNT(*) as total_files,
          SUM(file_size) as total_size
        FROM contracts
      `);

      // 格式化统计数据
      const stats = {
        users: {
          total: userStats.reduce((sum, item) => sum + item.count, 0),
          byRole: userStats.reduce((acc, item) => {
            acc[item.role] = item.count;
            return acc;
          }, {})
        },
        contracts: {
          total: contractStats.reduce((sum, item) => sum + item.count, 0),
          byStatus: contractStats.reduce((acc, item) => {
            acc[item.status] = item.count;
            return acc;
          }, {})
        },
        today: todayStats[0] || { total_today: 0, pending_today: 0, approved_today: 0 },
        files: fileStats || { total_files: 0, total_size: 0 },
        system: {
          version: '1.0.0',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          lastBackup: new Date().toISOString()
        }
      };

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error('获取系统统计错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: '获取系统统计失败'
      });
    }
  }
);

/**
 * 获取系统日志
 * GET /api/admin/system-logs
 */
router.get('/system-logs',
  authenticateToken,
  PermissionMiddleware.canManageSystem,
  async (req, res) => {
    try {
      const { page = 1, pageSize = 50, level = 'all' } = req.query;

      // 这里可以实现日志查询逻辑
      // 目前返回模拟数据
      const logs = [
        {
          id: 1,
          level: 'info',
          message: '用户登录成功',
          timestamp: new Date().toISOString(),
          user_id: req.user.id,
          ip: req.ip
        }
      ];

      const result = {
        data: logs,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: logs.length,
          totalPages: Math.ceil(logs.length / pageSize)
        }
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('获取系统日志错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: '获取系统日志失败'
      });
    }
  }
);

/**
 * 创建用户（管理员专用）
 * POST /api/admin/users
 */
router.post('/users',
  authenticateToken,
  PermissionMiddleware.canCreateUser,
  cacheInvalidation('admin', [
    () => {
      // 清理用户列表和统计缓存
      CacheHelpers.invalidateStatisticsCache();
    }
  ]),
  [
    body('username')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符'),
    body('role')
      .isIn(Object.values(USER_ROLES))
      .withMessage('无效的用户角色'),
    body('real_name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('真实姓名不能超过100个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { username, password, role, real_name, email, phone } = req.body;

      // 检查用户名是否已存在
      const existingUser = await UserModel.findByUsername(username);
      if (existingUser) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('用户名已存在', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 加密密码 - 使用较低的saltRounds减少CPU负载
      const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // 创建用户数据
      const userData = {
        username,
        password: hashedPassword,
        role,
        real_name: real_name || null,
        email: email || null,
        phone: phone || null,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      };

      console.log('👥 管理员创建用户 - 操作信息:', {
        adminId: req.user.id,
        adminUsername: req.user.username,
        newUsername: username,
        newRole: role
      });

      // 创建用户
      const userId = await UserModel.create(userData);
      const newUser = await UserModel.findById(userId);

      res.status(HTTP_STATUS.CREATED).json(
        ResponseUtils.success(newUser, '用户创建成功')
      );

    } catch (error) {
      console.error('管理员创建用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 更新用户（管理员专用）
 * PUT /api/admin/users/:id
 */
router.put('/users/:id',
  authenticateToken,
  PermissionMiddleware.canUpdateUser,
  cacheInvalidation('admin', [
    (req, res) => {
      // 清理相关缓存
      CacheHelpers.invalidateUserCache(req.params.id);
    }
  ]),
  [
    body('username')
      .optional()
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('role')
      .optional()
      .isIn(Object.values(USER_ROLES))
      .withMessage('无效的用户角色'),
    body('real_name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('真实姓名不能超过100个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'banned'])
      .withMessage('无效的用户状态')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const userId = parseInt(req.params.id);
      const { username, role, real_name, email, phone, status } = req.body;

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 不能修改自己的角色和状态
      if (userId === req.user.id && (role || status)) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('不能修改自己的角色或状态', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查用户是否存在
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 如果修改用户名，检查是否已存在
      if (username && username !== user.username) {
        const existingUser = await UserModel.findByUsername(username);
        if (existingUser) {
          return res.status(HTTP_STATUS.BAD_REQUEST).json(
            ResponseUtils.error('用户名已存在', HTTP_STATUS.BAD_REQUEST)
          );
        }
      }

      // 构建更新数据
      const updateData = { updated_at: new Date() };
      if (username) updateData.username = username;
      if (role) updateData.role = role;
      if (real_name !== undefined) updateData.real_name = real_name;
      if (email !== undefined) updateData.email = email;
      if (phone !== undefined) updateData.phone = phone;
      if (status) updateData.status = status;

      console.log('👥 管理员更新用户 - 操作信息:', {
        adminId: req.user.id,
        adminUsername: req.user.username,
        targetUserId: userId,
        targetUsername: user.username,
        updateData: Object.keys(updateData)
      });

      // 更新用户
      await UserModel.update(userId, updateData);
      const updatedUser = await UserModel.findById(userId);

      res.json(ResponseUtils.success(updatedUser, '用户更新成功'));

    } catch (error) {
      console.error('管理员更新用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 删除用户（管理员专用）
 * DELETE /api/admin/users/:id
 */
router.delete('/users/:id',
  authenticateToken,
  PermissionMiddleware.canDeleteUser,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 不能删除自己
      if (userId === req.user.id) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('不能删除自己的账号', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查用户是否存在
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      console.log('👥 管理员删除用户 - 操作信息:', {
        adminId: req.user.id,
        adminUsername: req.user.username,
        targetUserId: userId,
        targetUsername: user.username,
        targetRole: user.role
      });

      // 删除用户
      await UserModel.delete(userId);

      res.json(ResponseUtils.success(null, '用户删除成功'));

    } catch (error) {
      console.error('管理员删除用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 重置用户密码（管理员专用）
 * PUT /api/admin/users/:id/reset-password
 */
router.put('/users/:id/reset-password',
  authenticateToken,
  PermissionMiddleware.canManageUser,
  [
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const userId = parseInt(req.params.id);
      const { password } = req.body;

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查用户是否存在
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 加密新密码 - 使用较低的saltRounds减少CPU负载
      const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      console.log('👥 管理员重置密码 - 操作信息:', {
        adminId: req.user.id,
        adminUsername: req.user.username,
        targetUserId: userId,
        targetUsername: user.username
      });

      // 更新密码
      await UserModel.update(userId, {
        password: hashedPassword,
        updated_at: new Date()
      });

      res.json(ResponseUtils.success(null, '密码重置成功'));

    } catch (error) {
      console.error('管理员重置密码错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

// 临时路由，后续会在管理员功能开发任务中完善
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '管理员路由测试成功',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
