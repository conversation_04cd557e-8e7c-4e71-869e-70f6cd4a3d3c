/**
 * 邮件服务工具
 * 实现邮件发送和模板管理功能
 */

const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs');
const { SYSTEM_CONFIG } = require('./constants');

// 邮件配置
const EMAIL_CONFIG = {
  // SMTP 配置
  host: process.env.SMTP_HOST || 'smtp.163.com',
  port: process.env.SMTP_PORT || 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
};

// 创建邮件发送器
let transporter = null;

/**
 * 初始化邮件服务
 */
function initEmailService() {
  try {
    // 验证必需的环境变量
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      throw new Error('邮件服务配置错误：缺少必需的环境变量 SMTP_USER 或 SMTP_PASS');
    }

    transporter = nodemailer.createTransport(EMAIL_CONFIG);
    console.log('✅ 邮件服务初始化成功');
    return true;
  } catch (error) {
    console.error('❌ 邮件服务初始化失败:', error.message);
    return false;
  }
}

/**
 * 验证邮件服务连接
 */
async function verifyEmailService() {
  if (!transporter) {
    return false;
  }

  try {
    await transporter.verify();
    console.log('✅ 邮件服务连接验证成功');
    return true;
  } catch (error) {
    console.error('❌ 邮件服务连接验证失败:', error.message);
    return false;
  }
}

/**
 * 发送邮件
 * @param {Object} options - 邮件选项
 * @param {string} options.to - 收件人邮箱
 * @param {string} options.subject - 邮件主题
 * @param {string} options.html - 邮件HTML内容
 * @param {string} [options.text] - 邮件纯文本内容
 * @param {Array} [options.attachments] - 附件
 * @returns {Promise<boolean>} 发送结果
 */
async function sendEmail(options) {
  if (!transporter) {
    console.error('❌ 邮件服务未初始化');
    return false;
  }

  try {
    const { to, subject, html, text, attachments } = options;

    const mailOptions = {
      from: `"${SYSTEM_CONFIG.APP_NAME}" <${EMAIL_CONFIG.auth.user}>`,
      to,
      subject,
      html,
      text,
      attachments
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ 邮件发送成功:', info.messageId);
    return true;
  } catch (error) {
    console.error('❌ 邮件发送失败:', error.message);
    return false;
  }
}

/**
 * 邮件模板管理
 */
class EmailTemplate {
  /**
   * 合同提交通知邮件
   */
  static contractSubmitted(data) {
    const { contractId, serialNumber, submitterName, reviewerName, fileName } = data;

    return {
      subject: `【${SYSTEM_CONFIG.APP_NAME}】新合同审核通知`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2 style="color: #333; margin-bottom: 20px;">新合同审核通知</h2>
            
            <div style="background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <h3 style="color: #007bff; margin-top: 0;">合同信息</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">合同编号:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${serialNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">提交人:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${submitterName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">审核人:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${reviewerName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">文件名:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${fileName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; font-weight: bold;">提交时间:</td>
                  <td style="padding: 8px;">${new Date().toLocaleString('zh-CN')}</td>
                </tr>
              </table>
            </div>
            
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
              <p style="margin: 0; color: #856404;">
                <strong>提醒：</strong>请及时登录系统进行合同审核。
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/review-management" 
                 style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                立即审核
              </a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      `
    };
  }

  /**
   * 合同审核结果通知邮件
   */
  static contractReviewed(data) {
    const {
      contractId,
      serialNumber,
      submitterName,
      reviewerName,
      fileName,
      result,
      comment
    } = data;

    const isApproved = result === 'approved';
    const statusColor = isApproved ? '#28a745' : '#dc3545';
    const statusText = isApproved ? '通过' : '未通过';

    return {
      subject: `【${SYSTEM_CONFIG.APP_NAME}】合同审核结果通知`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2 style="color: #333; margin-bottom: 20px;">合同审核结果通知</h2>
            
            <div style="background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <h3 style="color: #007bff; margin-top: 0;">合同信息</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">合同编号:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${serialNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">提交人:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${submitterName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">审核人:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${reviewerName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold;">文件名:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${fileName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; font-weight: bold;">审核结果:</td>
                  <td style="padding: 8px;">
                    <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                  </td>
                </tr>
              </table>
            </div>
            
            ${comment ? `
            <div style="background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <h3 style="color: #007bff; margin-top: 0;">审核意见</h3>
              <p style="margin: 0; padding: 10px; background-color: #f8f9fa; border-radius: 3px;">
                ${comment}
              </p>
            </div>
            ` : ''}
            
            <div style="background-color: ${isApproved ? '#d4edda' : '#f8d7da'}; padding: 15px; border-radius: 5px; border-left: 4px solid ${statusColor};">
              <p style="margin: 0; color: ${isApproved ? '#155724' : '#721c24'};">
                <strong>${isApproved ? '恭喜！' : '提醒：'}</strong>
                ${isApproved ? '您的合同已通过审核。' : '您的合同未通过审核，请根据审核意见进行修改后重新提交。'}
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/my-contracts" 
                 style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                查看详情
              </a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      `
    };
  }

  /**
   * 密码重置通知邮件
   */
  static passwordReset(data) {
    const { username, newPassword } = data;

    return {
      subject: `【${SYSTEM_CONFIG.APP_NAME}】密码重置通知`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2 style="color: #333; margin-bottom: 20px;">密码重置通知</h2>
            
            <div style="background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <p>您好，${username}：</p>
              <p>您的账户密码已被管理员重置。新密码为：</p>
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                <code style="font-size: 18px; font-weight: bold; color: #dc3545;">${newPassword}</code>
              </div>
              <p>为了您的账户安全，请登录后立即修改密码。</p>
            </div>
            
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
              <p style="margin: 0; color: #856404;">
                <strong>安全提醒：</strong>请妥善保管您的密码，不要泄露给他人。
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/login" 
                 style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                立即登录
              </a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      `
    };
  }

  /**
   * 系统通知邮件
   */
  static systemNotification(data) {
    const { title, content, type = 'info' } = data;

    const typeColors = {
      info: '#007bff',
      success: '#28a745',
      warning: '#ffc107',
      danger: '#dc3545'
    };

    return {
      subject: `【${SYSTEM_CONFIG.APP_NAME}】${title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2 style="color: #333; margin-bottom: 20px;">${title}</h2>
            
            <div style="background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <div style="border-left: 4px solid ${typeColors[type]}; padding-left: 15px;">
                ${content}
              </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard" 
                 style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                访问系统
              </a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      `
    };
  }
}

/**
 * 邮件通知服务
 */
class EmailNotificationService {
  /**
   * 发送合同提交通知
   */
  static async sendContractSubmittedNotification(contractData, reviewerEmail) {
    const template = EmailTemplate.contractSubmitted(contractData);
    return await sendEmail({
      to: reviewerEmail,
      ...template
    });
  }

  /**
   * 发送合同审核结果通知
   */
  static async sendContractReviewedNotification(contractData, submitterEmail) {
    const template = EmailTemplate.contractReviewed(contractData);
    return await sendEmail({
      to: submitterEmail,
      ...template
    });
  }

  /**
   * 发送密码重置通知
   */
  static async sendPasswordResetNotification(userData, userEmail) {
    const template = EmailTemplate.passwordReset(userData);
    return await sendEmail({
      to: userEmail,
      ...template
    });
  }

  /**
   * 发送系统通知
   */
  static async sendSystemNotification(notificationData, userEmail) {
    const template = EmailTemplate.systemNotification(notificationData);
    return await sendEmail({
      to: userEmail,
      ...template
    });
  }
}

module.exports = {
  initEmailService,
  verifyEmailService,
  sendEmail,
  EmailTemplate,
  EmailNotificationService
};