{"name": "hetong-project", "version": "1.0.0", "description": "合同审核系统", "scripts": {"config": "./scripts/quick-deploy.sh", "dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\" --names \"backend,frontend\" --prefix name --kill-others-on-fail", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:bg": "nohup npm run dev > dev.log 2>&1 &", "start": "concurrently \"cd backend && npm start\" \"cd frontend && npm run dev\" --names \"backend,frontend\" --prefix name", "start:bg": "pm2 start ecosystem.config.js", "build": "cd frontend && npm run build", "init-db": "cd backend && npm run init-db", "backend:pm2": "pm2 start ecosystem.config.js --only hetong-backend", "frontend:pm2": "pm2 start ecosystem.config.js --only hetong-frontend", "stop": "pm2 stop all", "restart": "pm2 restart all", "delete": "pm2 delete all", "logs": "pm2 logs", "logs:backend": "pm2 logs hetong-backend", "logs:frontend": "pm2 logs hetong-frontend", "status": "pm2 status", "monit": "pm2 monit", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "devDependencies": {"@playwright/test": "^1.54.1", "concurrently": "^8.2.2"}, "keywords": ["contract", "review", "system"], "author": "Contract Review System", "license": "MIT"}