/**
 * 认证相关路由
 * 处理用户登录、登出、注册等认证功能
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();

const { UserModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { PasswordUtils, TokenUtils, ResponseUtils, ValidationUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES } = require('../utils/constants');
const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

/**
 * 获取客户端IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null);
}

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login',
  [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 20 })
      .withMessage('用户名长度应在3-20个字符之间'),
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { username, password } = req.body;

      // 查找用户
      const user = await UserModel.findByUsername(username);
      if (!user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.INVALID_CREDENTIALS, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 检查用户状态
      if (user.status === 'banned') {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.USER_BANNED, HTTP_STATUS.FORBIDDEN)
        );
      }

      // 验证密码
      const isPasswordValid = await PasswordUtils.verify(password, user.password);
      if (!isPasswordValid) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.INVALID_CREDENTIALS, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 生成JWT令牌
      const token = TokenUtils.generate({
        id: user.id,
        username: user.username,
        role: user.role
      });

      // 清除登录尝试记录
      if (req.clearLoginAttempts) {
        req.clearLoginAttempts();
      }

      // 记录登录日志
      try {
        await logOperation({
          userId: user.id,
          action: ACTIONS.LOGIN,
          resourceType: RESOURCE_TYPES.USER,
          resourceId: user.id.toString(),
          details: JSON.stringify({
            username: user.username,
            role: user.role,
            loginTime: new Date().toISOString(),
            method: 'POST',
            url: '/api/auth/login'
          }),
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'],
          status: 'success'
        });
      } catch (logError) {
        console.error('登录日志记录失败:', logError);
      }

      // 返回登录成功响应
      res.json(ResponseUtils.success({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          status: user.status,
          avatar: user.avatar
        }
      }, RESPONSE_MESSAGES.LOGIN_SUCCESS));

    } catch (error) {
      console.error('登录错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // 记录登出日志
    try {
      await logOperation({
        userId: req.user.id,
        action: ACTIONS.LOGOUT,
        resourceType: RESOURCE_TYPES.USER,
        resourceId: req.user.id.toString(),
        details: JSON.stringify({
          username: req.user.username,
          logoutTime: new Date().toISOString(),
          method: 'POST',
          url: '/api/auth/logout'
        }),
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'],
        status: 'success'
      });
    } catch (logError) {
      console.error('登出日志记录失败:', logError);
    }

    // 在实际应用中，可以将令牌加入黑名单
    // 这里简单返回成功响应
    res.json(ResponseUtils.success(null, RESPONSE_MESSAGES.LOGOUT_SUCCESS));
  } catch (error) {
    console.error('登出错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取当前用户信息
 * GET /api/auth/profile
 */
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await UserModel.findById(req.user.id);

    if (!user) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
      );
    }

    res.json(ResponseUtils.success({
      id: user.id,
      username: user.username,
      role: user.role,
      status: user.status,
      created_at: user.created_at
    }));

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 修改密码
 * PUT /api/auth/password
 */
router.put('/password',
  authenticateToken,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('当前密码不能为空'),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('新密码长度至少6个字符')
      .custom((value, { req }) => {
        if (value === req.body.currentPassword) {
          throw new Error('新密码不能与当前密码相同');
        }
        return true;
      }),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('确认密码与新密码不匹配');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { currentPassword, newPassword } = req.body;

      // 获取用户信息
      const user = await UserModel.findById(req.user.id);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.USER_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 验证当前密码
      const isCurrentPasswordValid = await PasswordUtils.verify(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('当前密码不正确', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 加密新密码
      const hashedNewPassword = await PasswordUtils.hash(newPassword);

      // 更新密码
      await UserModel.update(req.user.id, {
        password: hashedNewPassword
      });

      // 记录密码修改日志
      try {
        await logOperation({
          userId: req.user.id,
          action: ACTIONS.CHANGE_PASSWORD,
          resourceType: RESOURCE_TYPES.USER,
          resourceId: req.user.id.toString(),
          details: JSON.stringify({
            username: req.user.username,
            changeTime: new Date().toISOString(),
            method: 'PUT',
            url: '/api/auth/password'
          }),
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'],
          status: 'success'
        });
      } catch (logError) {
        console.error('密码修改日志记录失败:', logError);
      }

      res.json(ResponseUtils.success(null, '密码修改成功'));

    } catch (error) {
      console.error('修改密码错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 验证令牌有效性
 * GET /api/auth/verify
 */
router.get('/verify', authenticateToken, async (req, res) => {
  try {
    res.json(ResponseUtils.success({
      valid: true,
      user: req.user
    }, '令牌验证成功'));
  } catch (error) {
    console.error('令牌验证错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取用户权限
 * GET /api/auth/permissions/:userId
 */
router.get('/permissions/:userId', authenticateToken, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);

    // 检查用户是否有权限查看权限信息（只能查看自己的或管理员可以查看所有）
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 导入RBAC工具
    const { PermissionUtils } = require('../middleware/rbac');

    // 获取用户权限
    const permissions = await PermissionUtils.getUserPermissions(userId);
    const userRole = await PermissionUtils.getUserRole(userId);

    res.json(ResponseUtils.success({
      permissions: permissions.map(p => p.name),
      role: userRole,
      fullPermissions: permissions
    }, '获取用户权限成功'));

  } catch (error) {
    console.error('获取用户权限错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 刷新令牌
 * POST /api/auth/refresh
 */
router.post('/refresh', authenticateToken, async (req, res) => {
  try {
    // 生成新的JWT令牌
    const newToken = TokenUtils.generate({
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    });

    res.json(ResponseUtils.success({
      token: newToken
    }, '令牌刷新成功'));

  } catch (error) {
    console.error('令牌刷新错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

module.exports = router;
