<template>
  <div class="contract-detail-tab">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载合同详情...</p>
    </div>

    <!-- 合同详情内容 -->
    <div v-else-if="contract" class="detail-content">
      <!-- 合同基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><Document /></el-icon>
              <span>合同信息</span>
            </div>
            <div class="header-actions">
              <el-tag :type="getStatusColor(contract.status)" size="large">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="contract-info">
          <div class="info-item">
            <label class="info-label">流水号：</label>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件名：</label>
            <span class="info-value">{{ contract.filename }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件大小：</label>
            <span class="info-value">{{
              formatFileSize(contract.file_size)
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交人：</label>
            <span class="info-value">{{ contract.submitter_name }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">审核人：</label>
            <span class="info-value">{{
              contract.reviewer_name || "未分配"
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>

          <div class="info-item full-width">
            <label class="info-label">提交说明：</label>
            <span class="info-value">{{ contract.submit_note || "无" }}</span>
          </div>

          <!-- 审核时间 -->
          <div v-if="contract.reviewed_at" class="info-item">
            <label class="info-label">审核时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.reviewed_at)
            }}</span>
          </div>
        </div>

        <!-- 审核意见区域 -->
        <div v-if="contract.review_comment" class="review-section">
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核意见
          </h4>
          <div
            class="review-comment"
            :class="`review-comment--${contract.status}`"
          >
            {{ contract.review_comment }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="canModify(contract)"
            type="warning"
            @click="editContract"
          >
            <el-icon><Edit /></el-icon>
            修改合同
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- PDF 预览区域 - 独立显示在页面底部 -->
    <div v-if="contract && previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-content">
        <SimplePDFViewer
          :src="previewUrl"
          :filename="contract.filename"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        />
      </div>
    </div>

    <!-- PDF 预览错误状态 -->
    <div v-else-if="contract && !previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-placeholder">
        <el-icon :size="64" class="placeholder-icon">
          <DocumentCopy />
        </el-icon>
        <p class="placeholder-text">无法预览此文件</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-icon :size="64" class="error-icon">
        <Warning />
      </el-icon>
      <p class="error-text">合同不存在或已被删除</p>
    </div>

    <!-- 编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="contract"
      @updated="handleContractUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Loading,
  Document,
  Edit,
  Check,
  View,
  DocumentCopy,
  Warning,
  ChatDotRound,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import { useContracts } from "@/composables/useContracts";
import { filesAPI } from "@/api/files";

// 定义 props
const props = defineProps({
  contractId: {
    type: [Number, String],
    required: true,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["edit", "review", "updated"]);

// 合同管理
const {
  getContractDetail,
  canModify,
  canReview,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  startReview,
  submitting,
} = useContracts();

// 响应式数据
const loading = ref(false);
const contract = ref(props.contract);
const showEditDialog = ref(false);

// 预览相关 - 使用不带token的URL，让SimplePDFViewer自动添加token
const previewUrl = computed(() => {
  if (contract.value && contract.value.id) {
    return filesAPI.getContractPreviewUrl(contract.value.id);
  }
  return null;
});

// 加载合同详情
const loadContractDetail = async () => {
  if (props.contract) {
    contract.value = props.contract;
    return;
  }

  loading.value = true;
  try {
    const result = await getContractDetail(props.contractId);
    contract.value = result;
  } finally {
    loading.value = false;
  }
};

// 监听 props 变化
watch(
  () => props.contractId,
  (newId) => {
    if (newId) {
      loadContractDetail();
    }
  },
  { immediate: true },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract) {
      contract.value = newContract;
    }
  },
  { immediate: true },
);

// 编辑合同
const editContract = () => {
  showEditDialog.value = true;
};

// 处理合同更新
const handleContractUpdated = (updatedContract) => {
  contract.value = updatedContract;
  emit("updated", updatedContract);
  ElMessage.success("合同修改成功");
};

// PDF预览事件处理
const handlePreviewLoad = () => {
  console.log("PDF预览加载成功");
};

const handlePreviewError = (error) => {
  console.error("PDF预览加载失败:", error);
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.contractId && !contract.value) {
    loadContractDetail();
  }
});
</script>

<style scoped>
.contract-detail-tab {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.loading-icon {
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.contract-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.info-label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #303133;
  word-break: break-all;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* PDF 预览区域样式 */
.pdf-preview-section {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  padding: 0;
  background: #fff;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  background: #fafafa;
}

.placeholder-icon {
  margin-bottom: 16px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #f56c6c;
}

.error-icon {
  margin-bottom: 16px;
}

.error-text {
  margin-bottom: 16px;
  font-size: 16px;
}

/* 审核意见区域 */
.review-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.review-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.review-comment {
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.review-comment--approved {
  border-left: 4px solid #67c23a;
  background: #f0f9ff;
}

.review-comment--rejected {
  border-left: 4px solid #f56c6c;
  background: #fef0f0;
}
</style>
