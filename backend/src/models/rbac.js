/**
 * RBAC (基于角色的访问控制) 数据模型
 * 提供角色、权限和用户权限管理的数据库操作方法
 */

const { database } = require('../utils/database');

/**
 * 角色模型
 */
const RoleModel = {
  /**
   * 获取所有角色
   */
  async findAll() {
    const sql = `
      SELECT id, name, display_name, description, level, is_active, created_at
      FROM roles 
      WHERE is_active = TRUE
      ORDER BY level ASC
    `;
    return await database.all(sql);
  },

  /**
   * 根据名称查找角色
   */
  async findByName(name) {
    const sql = 'SELECT * FROM roles WHERE name = ? AND is_active = TRUE';
    return await database.get(sql, [name]);
  },

  /**
   * 根据ID查找角色
   */
  async findById(id) {
    const sql = 'SELECT * FROM roles WHERE id = ? AND is_active = TRUE';
    return await database.get(sql, [id]);
  },

  /**
   * 创建角色
   */
  async create(roleData) {
    const { name, display_name, description, level = 0 } = roleData;
    const sql = `
      INSERT INTO roles (name, display_name, description, level)
      VALUES (?, ?, ?, ?)
    `;
    return await database.run(sql, [name, display_name, description, level]);
  },

  /**
   * 更新角色
   */
  async update(id, roleData) {
    const { display_name, description, level } = roleData;
    const sql = `
      UPDATE roles 
      SET display_name = ?, description = ?, level = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    return await database.run(sql, [display_name, description, level, id]);
  },

  /**
   * 删除角色（软删除）
   */
  async delete(id) {
    const sql = 'UPDATE roles SET is_active = FALSE WHERE id = ?';
    return await database.run(sql, [id]);
  }
};

/**
 * 权限模型
 */
const PermissionModel = {
  /**
   * 获取所有权限
   */
  async findAll() {
    const sql = `
      SELECT id, name, action, subject, description, category, is_active, created_at
      FROM permissions 
      WHERE is_active = TRUE
      ORDER BY category, name
    `;
    return await database.all(sql);
  },

  /**
   * 根据分类获取权限
   */
  async findByCategory(category) {
    const sql = `
      SELECT id, name, action, subject, description, category, is_active, created_at
      FROM permissions 
      WHERE category = ? AND is_active = TRUE
      ORDER BY name
    `;
    return await database.all(sql, [category]);
  },

  /**
   * 根据名称查找权限
   */
  async findByName(name) {
    const sql = 'SELECT * FROM permissions WHERE name = ? AND is_active = TRUE';
    return await database.get(sql, [name]);
  },

  /**
   * 根据ID查找权限
   */
  async findById(id) {
    const sql = 'SELECT * FROM permissions WHERE id = ? AND is_active = TRUE';
    return await database.get(sql, [id]);
  },

  /**
   * 创建权限
   */
  async create(permissionData) {
    const { name, action, subject, description, category = 'general' } = permissionData;
    const sql = `
      INSERT INTO permissions (name, action, subject, description, category)
      VALUES (?, ?, ?, ?, ?)
    `;
    return await database.run(sql, [name, action, subject, description, category]);
  },

  /**
   * 更新权限
   */
  async update(id, permissionData) {
    const { action, subject, description, category } = permissionData;
    const sql = `
      UPDATE permissions 
      SET action = ?, subject = ?, description = ?, category = ?
      WHERE id = ?
    `;
    return await database.run(sql, [action, subject, description, category, id]);
  },

  /**
   * 删除权限（软删除）
   */
  async delete(id) {
    const sql = 'UPDATE permissions SET is_active = FALSE WHERE id = ?';
    return await database.run(sql, [id]);
  }
};

/**
 * 角色权限关联模型
 */
const RolePermissionModel = {
  /**
   * 获取角色的所有权限
   */
  async findPermissionsByRole(roleId) {
    const sql = `
      SELECT p.id, p.name, p.action, p.subject, p.description, p.category
      FROM permissions p
      INNER JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ? AND p.is_active = TRUE
      ORDER BY p.category, p.name
    `;
    return await database.all(sql, [roleId]);
  },

  /**
   * 获取权限的所有角色
   */
  async findRolesByPermission(permissionId) {
    const sql = `
      SELECT r.id, r.name, r.display_name, r.description, r.level
      FROM roles r
      INNER JOIN role_permissions rp ON r.id = rp.role_id
      WHERE rp.permission_id = ? AND r.is_active = TRUE
      ORDER BY r.level
    `;
    return await database.all(sql, [permissionId]);
  },

  /**
   * 为角色分配权限
   */
  async assignPermission(roleId, permissionId, grantedBy = null) {
    const sql = `
      INSERT OR IGNORE INTO role_permissions (role_id, permission_id, granted_by)
      VALUES (?, ?, ?)
    `;
    return await database.run(sql, [roleId, permissionId, grantedBy]);
  },

  /**
   * 移除角色权限
   */
  async removePermission(roleId, permissionId) {
    const sql = 'DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?';
    return await database.run(sql, [roleId, permissionId]);
  },

  /**
   * 批量设置角色权限
   */
  async setRolePermissions(roleId, permissionIds, grantedBy = null) {
    // 开始事务
    await database.run('BEGIN TRANSACTION');
    
    try {
      // 清除现有权限
      await database.run('DELETE FROM role_permissions WHERE role_id = ?', [roleId]);
      
      // 添加新权限
      for (const permissionId of permissionIds) {
        await this.assignPermission(roleId, permissionId, grantedBy);
      }
      
      // 提交事务
      await database.run('COMMIT');
      return { success: true };
    } catch (error) {
      // 回滚事务
      await database.run('ROLLBACK');
      throw error;
    }
  },

  /**
   * 检查角色是否拥有特定权限
   */
  async hasPermission(roleId, permissionName) {
    const sql = `
      SELECT COUNT(*) as count
      FROM role_permissions rp
      INNER JOIN permissions p ON rp.permission_id = p.id
      WHERE rp.role_id = ? AND p.name = ? AND p.is_active = TRUE
    `;
    const result = await database.get(sql, [roleId, permissionName]);
    return result.count > 0;
  }
};

/**
 * 用户权限模型（扩展现有UserModel）
 */
const UserPermissionModel = {
  /**
   * 获取用户的所有权限
   */
  async getUserPermissions(userId) {
    const sql = `
      SELECT DISTINCT p.id, p.name, p.action, p.subject, p.description, p.category
      FROM permissions p
      INNER JOIN role_permissions rp ON p.id = rp.permission_id
      INNER JOIN users u ON rp.role_id = u.role_id
      WHERE u.id = ? AND p.is_active = TRUE AND u.status = 'active'
      ORDER BY p.category, p.name
    `;
    return await database.all(sql, [userId]);
  },

  /**
   * 检查用户是否拥有特定权限
   */
  async userHasPermission(userId, permissionName) {
    const sql = `
      SELECT COUNT(*) as count
      FROM permissions p
      INNER JOIN role_permissions rp ON p.id = rp.permission_id
      INNER JOIN users u ON rp.role_id = u.role_id
      WHERE u.id = ? AND p.name = ? AND p.is_active = TRUE AND u.status = 'active'
    `;
    const result = await database.get(sql, [userId, permissionName]);
    return result.count > 0;
  },

  /**
   * 获取用户角色信息
   */
  async getUserRole(userId) {
    const sql = `
      SELECT r.id, r.name, r.display_name, r.description, r.level
      FROM roles r
      INNER JOIN users u ON r.id = u.role_id
      WHERE u.id = ? AND r.is_active = TRUE AND u.status = 'active'
    `;
    return await database.get(sql, [userId]);
  },

  /**
   * 更新用户角色
   */
  async updateUserRole(userId, roleId) {
    const sql = 'UPDATE users SET role_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    return await database.run(sql, [roleId, userId]);
  }
};

module.exports = {
  RoleModel,
  PermissionModel,
  RolePermissionModel,
  UserPermissionModel
};
