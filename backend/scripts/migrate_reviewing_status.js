/**
 * 数据库迁移脚本：将所有reviewing状态的合同改为pending状态
 * 运行方式：node backend/scripts/migrate_reviewing_status.js
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const dbPath = DATABASE_CONFIG.path;

console.log('开始迁移数据库...');
console.log('数据库路径:', dbPath);

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 执行迁移
async function migrate() {
  return new Promise((resolve, reject) => {
    // 首先查询有多少reviewing状态的合同
    db.get("SELECT COUNT(*) as count FROM contracts WHERE status = 'reviewing'", (err, row) => {
      if (err) {
        console.error('查询失败:', err.message);
        reject(err);
        return;
      }

      const reviewingCount = row.count;
      console.log(`📊 发现 ${reviewingCount} 个reviewing状态的合同`);

      if (reviewingCount === 0) {
        console.log('✅ 没有需要迁移的数据');
        resolve();
        return;
      }

      // 更新reviewing状态为pending
      db.run(
        "UPDATE contracts SET status = 'pending' WHERE status = 'reviewing'",
        function (err) {
          if (err) {
            console.error('更新失败:', err.message);
            reject(err);
            return;
          }

          console.log(`✅ 成功更新 ${this.changes} 条记录`);
          console.log('✅ 数据迁移完成');
          resolve();
        }
      );
    });
  });
}

// 执行迁移并关闭数据库连接
migrate()
  .then(() => {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
      process.exit(0);
    });
  })
  .catch((err) => {
    console.error('❌ 迁移失败:', err);
    db.close();
    process.exit(1);
  });
