const { chromium } = require('playwright');

async function testReviewValidation() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🚀 开始测试审核验证功能...');

    // 1. 登录审核员账户
    console.log('📝 登录审核员账户...');
    await page.goto('http://localhost:5173/login');
    await page.waitForSelector('input[placeholder="请输入用户名"]');
    
    await page.fill('input[placeholder="请输入用户名"]', 'reviewer');
    await page.fill('input[placeholder="请输入密码"]', '123456');
    await page.click('button[type="submit"]');
    
    // 等待登录成功
    await page.waitForURL('**/dashboard');
    console.log('✅ 登录成功');

    // 2. 进入合同列表页面
    console.log('📋 进入合同列表页面...');
    await page.goto('http://localhost:5173/contracts');
    await page.waitForSelector('.contract-table');

    // 3. 找到第一个待审核的合同并点击审核
    console.log('🔍 查找待审核合同...');
    const reviewButton = await page.locator('button:has-text("审核")').first();
    await reviewButton.click();

    // 等待审核对话框出现
    await page.waitForSelector('.el-dialog');
    console.log('✅ 审核对话框已打开');

    // 测试场景1：审核通过时不填写说明
    console.log('\n🧪 测试场景1：审核通过时不填写说明');
    
    // 选择"通过"
    await page.click('input[value="approved"]');
    console.log('✅ 已选择审核通过');
    
    // 不填写审核意见，直接点击提交
    const submitButton = page.locator('button:has-text("提交审核结果")');
    
    // 检查按钮是否可用
    const isDisabled = await submitButton.getAttribute('disabled');
    if (isDisabled === null) {
      console.log('✅ 审核通过时，按钮可用（说明可选）');
      
      // 点击提交按钮
      await submitButton.click();
      
      // 等待确认对话框
      await page.waitForSelector('.el-message-box');
      await page.click('button:has-text("确定")');
      
      // 等待提交完成
      await page.waitForSelector('.el-message--success', { timeout: 5000 });
      console.log('✅ 审核通过（无说明）提交成功');
      
      // 关闭成功消息
      await page.waitForTimeout(2000);
    } else {
      console.log('❌ 审核通过时按钮仍被禁用');
    }

    // 如果第一个测试成功，寻找下一个合同测试场景2
    console.log('\n🔍 查找下一个待审核合同...');
    await page.goto('http://localhost:5173/contracts');
    await page.waitForSelector('.contract-table');
    
    const nextReviewButton = await page.locator('button:has-text("审核")').first();
    if (await nextReviewButton.count() > 0) {
      await nextReviewButton.click();
      await page.waitForSelector('.el-dialog');

      // 测试场景2：审核通过时填写少于10字符说明
      console.log('\n🧪 测试场景2：审核通过时填写少于10字符说明');
      
      await page.click('input[value="approved"]');
      await page.fill('textarea[placeholder*="审核意见"]', '同意');
      console.log('✅ 已填写少于10字符的说明："同意"');
      
      const submitButton2 = page.locator('button:has-text("提交审核结果")');
      const isDisabled2 = await submitButton2.getAttribute('disabled');
      
      if (isDisabled2 === null) {
        console.log('✅ 审核通过时，少于10字符说明也可提交');
        
        await submitButton2.click();
        await page.waitForSelector('.el-message-box');
        await page.click('button:has-text("确定")');
        await page.waitForSelector('.el-message--success', { timeout: 5000 });
        console.log('✅ 审核通过（少于10字符说明）提交成功');
        
        await page.waitForTimeout(2000);
      } else {
        console.log('❌ 审核通过时少于10字符说明仍被禁用');
      }
    }

    // 查找下一个合同测试驳回场景
    console.log('\n🔍 查找下一个待审核合同测试驳回场景...');
    await page.goto('http://localhost:5173/contracts');
    await page.waitForSelector('.contract-table');
    
    const rejectTestButton = await page.locator('button:has-text("审核")').first();
    if (await rejectTestButton.count() > 0) {
      await rejectTestButton.click();
      await page.waitForSelector('.el-dialog');

      // 测试场景3：审核驳回时不填写说明
      console.log('\n🧪 测试场景3：审核驳回时不填写说明');
      
      await page.click('input[value="rejected"]');
      console.log('✅ 已选择审核驳回');
      
      const submitButton3 = page.locator('button:has-text("提交审核结果")');
      const isDisabled3 = await submitButton3.getAttribute('disabled');
      
      if (isDisabled3 !== null) {
        console.log('✅ 审核驳回时不填写说明，按钮被正确禁用');
      } else {
        console.log('❌ 审核驳回时不填写说明，按钮仍可用（应该被禁用）');
      }

      // 测试场景4：审核驳回时填写少于10字符说明
      console.log('\n🧪 测试场景4：审核驳回时填写少于10字符说明');
      
      await page.fill('textarea[placeholder*="审核意见"]', '不行');
      console.log('✅ 已填写少于10字符的驳回说明："不行"');
      
      const isDisabled4 = await submitButton3.getAttribute('disabled');
      if (isDisabled4 !== null) {
        console.log('✅ 审核驳回时少于10字符说明，按钮被正确禁用');
      } else {
        console.log('❌ 审核驳回时少于10字符说明，按钮仍可用（应该被禁用）');
      }

      // 测试场景5：审核驳回时填写10字符以上说明
      console.log('\n🧪 测试场景5：审核驳回时填写10字符以上说明');
      
      await page.fill('textarea[placeholder*="审核意见"]', '合同内容不符合要求，请重新修改后提交');
      console.log('✅ 已填写10字符以上的驳回说明');
      
      const isDisabled5 = await submitButton3.getAttribute('disabled');
      if (isDisabled5 === null) {
        console.log('✅ 审核驳回时10字符以上说明，按钮可用');
        
        // 可以选择实际提交测试
        console.log('💡 可以提交，但为了保留测试数据，跳过实际提交');
      } else {
        console.log('❌ 审核驳回时10字符以上说明，按钮仍被禁用');
      }
    }

    console.log('\n🎉 所有测试场景完成！');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testReviewValidation().catch(console.error);
