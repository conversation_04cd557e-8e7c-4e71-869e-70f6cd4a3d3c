/**
 * 检查数据库结构脚本
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const dbPath = DATABASE_CONFIG.path;

console.log('检查数据库结构...');
console.log('数据库路径:', dbPath);

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 查看所有表
db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
  if (err) {
    console.error('查询表失败:', err.message);
    db.close();
    process.exit(1);
  }

  console.log('📊 数据库中的表:');
  tables.forEach(table => {
    console.log(`  - ${table.name}`);
  });

  // 如果有contracts表，查看其结构和数据
  const contractsTable = tables.find(t => t.name === 'contracts');
  if (contractsTable) {
    console.log('\n📋 contracts表结构:');
    db.all("PRAGMA table_info(contracts)", (err, columns) => {
      if (err) {
        console.error('查询表结构失败:', err.message);
      } else {
        columns.forEach(col => {
          console.log(`  - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`);
        });
      }

      // 查看reviewing状态的数据
      db.all("SELECT id, serial_number, status FROM contracts WHERE status = 'reviewing'", (err, rows) => {
        if (err) {
          console.error('查询reviewing状态数据失败:', err.message);
        } else {
          console.log(`\n📊 reviewing状态的合同数量: ${rows.length}`);
          if (rows.length > 0) {
            console.log('详细信息:');
            rows.forEach(row => {
              console.log(`  - ID: ${row.id}, 编号: ${row.serial_number}, 状态: ${row.status}`);
            });
          }
        }

        db.close();
      });
    });
  } else {
    console.log('\n❌ 未找到contracts表');
    db.close();
  }
});
