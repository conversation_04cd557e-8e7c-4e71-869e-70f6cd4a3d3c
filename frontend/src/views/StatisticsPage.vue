<!--
  审核统计页面
  显示合同审核的统计数据和图表
-->
<template>
  <div class="statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">审核统计</h1>
        <p class="page-description">查看合同审核的统计数据和趋势分析</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon :size="24"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ totalContracts }}</div>
                <div class="stats-label">全部</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon reviewing">
                <el-icon :size="24"><View /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ reviewingContracts }}</div>
                <div class="stats-label">审核中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon approved">
                <el-icon :size="24"><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ approvedContracts }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rejected">
                <el-icon :size="24"><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ rejectedContracts }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>审核趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="trendChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>审核分布</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="distributionChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div class="detailed-stats">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细统计</span>
            <el-button type="primary" size="small">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </template>

        <el-table :data="detailedStats" style="width: 100%">
          <el-table-column prop="period" label="时间段" width="120" />
          <el-table-column prop="total" label="全部" width="100" />
          <el-table-column prop="reviewing" label="审核中" width="100" />
          <el-table-column prop="approved" label="已通过" width="100" />
          <el-table-column prop="rejected" label="已拒绝" width="100" />
          <el-table-column prop="efficiency" label="审核效率" width="120">
            <template #default="scope">
              <el-tag :type="getEfficiencyType(scope.row.efficiency)">
                {{ scope.row.efficiency }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="avgTime" label="平均用时" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart, PieChart as EChartsPieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import VChart, { THEME_KEY } from "vue-echarts";
import {
  Document,
  Clock,
  View,
  Check,
  Close,
  DataAnalysis,
  PieChart,
  Download,
} from "@element-plus/icons-vue";
import { statisticsAPI } from "@/api/statistics";
import { ElMessage } from "element-plus";

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  EChartsPieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

// 响应式数据
const loading = ref(false);
const chartsLoading = ref(false);
const totalContracts = ref(0);
const reviewingContracts = ref(0);
const approvedContracts = ref(0);
const rejectedContracts = ref(0);

// 图表配置
const trendChartOption = ref({});
const distributionChartOption = ref({});

const detailedStats = ref([]);

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true;
    chartsLoading.value = true;

    // 获取汇总数据
    const summaryResponse = await statisticsAPI.getSummary();
    if (summaryResponse.success) {
      const data = summaryResponse.data.overview;
      totalContracts.value = data.totalAssigned || data.totalContracts || 0;
      reviewingContracts.value = data.reviewing || 0;
      approvedContracts.value = data.approved || 0;
      rejectedContracts.value = data.rejected || 0;
    }

    // 获取趋势数据
    const trendsResponse = await statisticsAPI.getTrends();
    if (trendsResponse.success) {
      setupTrendChart(trendsResponse.data);
    }

    // 获取分布数据
    const distributionResponse = await statisticsAPI.getDistribution();
    if (distributionResponse.success) {
      setupDistributionChart(distributionResponse.data);
    }

    // 设置详细统计数据
    detailedStats.value = [
      {
        period: "本周",
        total: totalContracts.value,
        reviewing: reviewingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: "2.3天",
      },
      {
        period: "本月",
        total: totalContracts.value,
        reviewing: reviewingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: "2.8天",
      },
    ];
  } catch (error) {
    console.error("加载统计数据失败:", error);
    ElMessage.error("加载统计数据失败");
  } finally {
    loading.value = false;
    chartsLoading.value = false;
  }
};

// 配置趋势图表
const setupTrendChart = (data) => {
  const dates = data.trends?.map((item) => item.date) || [];
  const totals = data.trends?.map((item) => item.total) || [];
  const completed = data.trends?.map((item) => item.completed) || [];

  trendChartOption.value = {
    title: {
      text: "审核趋势",
      left: "center",
      textStyle: {
        fontSize: 16,
        color: "#333",
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    legend: {
      data: ["总数", "已完成"],
      bottom: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data:
        dates.length > 0 ? dates : ["1月", "2月", "3月", "4月", "5月", "6月"],
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
        },
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
        },
      },
    },
    series: [
      {
        name: "总数",
        type: "line",
        data: totals.length > 0 ? totals : [20, 35, 45, 38, 52, 48],
        smooth: true,
        itemStyle: {
          color: "#409eff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(64, 158, 255, 0.3)" },
              { offset: 1, color: "rgba(64, 158, 255, 0.1)" },
            ],
          },
        },
      },
      {
        name: "已通过",
        type: "line",
        data: completed.length > 0 ? completed : [15, 28, 38, 32, 45, 42],
        smooth: true,
        itemStyle: {
          color: "#67c23a",
        },
      },
    ],
  };
};

// 配置分布图表
const setupDistributionChart = (data) => {
  const distributionData = data.distribution || [
    { name: "审核中", value: reviewingContracts.value },
    { name: "已通过", value: approvedContracts.value },
    { name: "已拒绝", value: rejectedContracts.value },
  ];

  distributionChartOption.value = {
    title: {
      text: "审核分布",
      left: "center",
      textStyle: {
        fontSize: 16,
        color: "#333",
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: "horizontal",
      bottom: 10,
    },
    series: [
      {
        name: "审核分布",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["50%", "50%"],
        data: distributionData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        itemStyle: {
          borderRadius: 8,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: true,
          formatter: "{b}: {c}",
        },
      },
    ],
    color: ["#e6a23c", "#67c23a", "#f56c6c"],
  };
};

// 方法
const getEfficiencyType = (efficiency) => {
  if (efficiency >= 90) return "success";
  if (efficiency >= 80) return "warning";
  return "danger";
};

// 生命周期
onMounted(() => {
  loadStatistics();
});
</script>

<style scoped>
.statistics-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  color: #606266;
  margin: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.reviewing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.placeholder-text {
  font-size: 12px;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailed-stats {
  margin-bottom: 20px;
}
</style>
