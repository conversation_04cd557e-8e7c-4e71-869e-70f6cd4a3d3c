/**
 * RBAC (基于角色的访问控制) 中间件
 * 提供基于数据库角色和权限的访问控制
 */

const { UserPermissionModel, RoleModel } = require('../models/rbac');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES } = require('../utils/constants');

/**
 * 权限检查中间件
 * @param {string|string[]} requiredPermissions - 需要的权限名称
 * @param {object} options - 选项配置
 * @returns {Function} Express中间件函数
 */
const requirePermission = (requiredPermissions, options = {}) => {
  return async (req, res, next) => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userId = req.user.id;
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

      // 获取用户权限
      const userPermissions = await UserPermissionModel.getUserPermissions(userId);
      const userPermissionNames = userPermissions.map(p => p.name);

      // 检查权限逻辑
      const { requireAll = false } = options;
      let hasPermission = false;

      if (requireAll) {
        // 需要拥有所有权限
        hasPermission = permissions.every(permission => userPermissionNames.includes(permission));
      } else {
        // 需要拥有任一权限
        hasPermission = permissions.some(permission => userPermissionNames.includes(permission));
      }

      if (!hasPermission) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN, {
            required: permissions,
            user_permissions: userPermissionNames
          })
        );
      }

      // 将用户权限添加到请求对象中，供后续使用
      req.userPermissions = userPermissionNames;
      next();

    } catch (error) {
      console.error('权限检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 角色检查中间件
 * @param {string|string[]} requiredRoles - 需要的角色名称
 * @param {object} options - 选项配置
 * @returns {Function} Express中间件函数
 */
const requireRole = (requiredRoles, options = {}) => {
  return async (req, res, next) => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userId = req.user.id;
      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

      // 获取用户角色
      const userRole = await UserPermissionModel.getUserRole(userId);

      if (!userRole) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('用户角色未设置', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查角色
      const hasRole = roles.includes(userRole.name);

      if (!hasRole) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('角色权限不足', HTTP_STATUS.FORBIDDEN, {
            required: roles,
            user_role: userRole.name
          })
        );
      }

      // 将用户角色添加到请求对象中
      req.userRole = userRole;
      next();

    } catch (error) {
      console.error('角色检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 角色级别检查中间件
 * @param {number} minLevel - 最低角色级别
 * @returns {Function} Express中间件函数
 */
const requireMinLevel = (minLevel) => {
  return async (req, res, next) => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userId = req.user.id;

      // 获取用户角色
      const userRole = await UserPermissionModel.getUserRole(userId);

      if (!userRole) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('用户角色未设置', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查角色级别
      if (userRole.level < minLevel) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('角色级别不足', HTTP_STATUS.FORBIDDEN, {
            required_level: minLevel,
            user_level: userRole.level,
            user_role: userRole.name
          })
        );
      }

      // 将用户角色添加到请求对象中
      req.userRole = userRole;
      next();

    } catch (error) {
      console.error('角色级别检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 预定义的权限中间件
 */
const PermissionMiddleware = {
  // 合同权限
  canCreateContract: requirePermission('contract:create'),
  canReadContract: requirePermission('contract:read'),
  canUpdateContract: requirePermission('contract:update'),
  canDeleteContract: requirePermission('contract:delete'),
  canReviewContract: requirePermission(['contract:review', 'contract:approve', 'contract:reject'], { requireAll: false }),

  // 用户权限
  canCreateUser: requirePermission('user:create'),
  canReadUser: requirePermission('user:read'),
  canUpdateUser: requirePermission('user:update'),
  canDeleteUser: requirePermission('user:delete'),
  canManageUser: requirePermission('user:manage'),

  // 系统权限
  canViewStats: requirePermission('system:stats'),
  canManageSystem: requirePermission('system:manage'),

  // 通知权限
  canReadNotification: requirePermission('notification:read'),
  canCreateNotification: requirePermission('notification:create'),
  canManageNotification: requirePermission('notification:manage')
};

/**
 * 预定义的角色中间件
 */
const RoleMiddleware = {
  requireEmployee: requireRole('employee'),
  requireCountyReviewer: requireRole('county_reviewer'),
  requireCityReviewer: requireRole('city_reviewer'),
  requireAnyReviewer: requireRole(['county_reviewer', 'city_reviewer']),
  requireAdmin: requireRole('admin'),

  // 级别要求
  requireReviewerLevel: requireMinLevel(2), // 审核员级别及以上
  requireAdminLevel: requireMinLevel(4)     // 管理员级别
};

/**
 * 权限工具函数
 */
const PermissionUtils = {
  /**
   * 检查用户是否拥有权限
   */
  async checkUserPermission(userId, permission) {
    try {
      return await UserPermissionModel.userHasPermission(userId, permission);
    } catch (error) {
      console.error('检查用户权限错误:', error);
      return false;
    }
  },

  /**
   * 获取用户所有权限
   */
  async getUserPermissions(userId) {
    try {
      return await UserPermissionModel.getUserPermissions(userId);
    } catch (error) {
      console.error('获取用户权限错误:', error);
      return [];
    }
  },

  /**
   * 获取用户角色
   */
  async getUserRole(userId) {
    try {
      return await UserPermissionModel.getUserRole(userId);
    } catch (error) {
      console.error('获取用户角色错误:', error);
      return null;
    }
  }
};

module.exports = {
  requirePermission,
  requireRole,
  requireMinLevel,
  PermissionMiddleware,
  RoleMiddleware,
  PermissionUtils
};
