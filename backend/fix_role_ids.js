const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

console.log('🔧 修复用户role_id字段...\n');

// 修复role_id字段
async function fixRoleIds() {
  return new Promise((resolve, reject) => {
    // 获取所有角色
    db.all("SELECT id, name FROM roles", (err, roles) => {
      if (err) {
        reject(err);
        return;
      }

      const roleMap = {};
      roles.forEach(role => {
        roleMap[role.name] = role.id;
      });

      console.log('📋 角色映射:');
      console.table(roleMap);

      // 更新所有用户的role_id
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 更新每个角色的用户
        Object.keys(roleMap).forEach(roleName => {
          const roleId = roleMap[roleName];
          db.run(
            "UPDATE users SET role_id = ? WHERE role = ? AND role_id IS NULL",
            [roleId, roleName],
            function (err) {
              if (err) {
                console.error(`❌ 更新${roleName}失败:`, err.message);
              } else {
                console.log(`✅ 更新${roleName}用户: ${this.changes}个`);
              }
            }
          );
        });

        db.run("COMMIT", (err) => {
          if (err) {
            reject(err);
          } else {
            console.log('\n🎉 role_id修复完成！');
            resolve();
          }
        });
      });
    });
  });
}

// 验证修复结果
async function verifyFix() {
  return new Promise((resolve, reject) => {
    db.all("SELECT id, username, role, role_id FROM users WHERE role_id IS NULL", (err, rows) => {
      if (err) {
        reject(err);
        return;
      }

      if (rows.length === 0) {
        console.log('✅ 所有用户的role_id都已正确设置');
      } else {
        console.log('⚠️  仍有用户的role_id为空:');
        console.table(rows);
      }

      resolve();
    });
  });
}

// 执行修复
fixRoleIds()
  .then(() => verifyFix())
  .then(() => {
    console.log('\n🎉 修复完成！');
    db.close();
  })
  .catch(err => {
    console.error('❌ 修复失败:', err.message);
    db.close();
    process.exit(1);
  });
