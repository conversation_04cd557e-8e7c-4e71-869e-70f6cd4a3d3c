<template>
  <div class="notifications-page">
    <div class="page-header">
      <h2 class="page-title">通知中心</h2>
      <div class="header-actions">
        <el-button
          v-if="notificationStore.unreadCount > 0"
          type="primary"
          :loading="markingAllRead"
          @click="markAllAsRead"
        >
          全部标记为已读
        </el-button>
        <el-button
          :loading="notificationStore.loading"
          @click="refreshNotifications"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-bar">
      <el-form :inline="true" :model="filters" class="filter-form">
        <el-form-item label="状态">
          <el-select
            v-model="filters.is_read"
            placeholder="全部"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部" value="" />
            <el-option label="未读" :value="false" />
            <el-option label="已读" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="filters.type"
            placeholder="全部类型"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部类型" value="" />
            <el-option label="合同提交" value="contract_submitted" />
            <el-option label="合同审核通过" value="contract_approved" />
            <el-option label="合同审核拒绝" value="contract_rejected" />
            <el-option label="合同分配" value="contract_assigned" />
            <el-option label="系统通知" value="system_notice" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 通知列表 -->
    <div v-loading="notificationStore.loading" class="notifications-container">
      <div
        v-if="notificationStore.notifications.length === 0"
        class="empty-state"
      >
        <el-icon :size="64" color="#c0c4cc"><Bell /></el-icon>
        <p class="empty-text">暂无通知消息</p>
      </div>

      <div v-else class="notifications-list">
        <div
          v-for="notification in notificationStore.notifications"
          :key="notification.id"
          class="notification-card"
          :class="{ 'notification-card--unread': !notification.is_read }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-icon">
            <el-icon
              :size="20"
              :color="getNotificationIconColor(notification.type)"
            >
              <component :is="getNotificationIcon(notification.type)" />
            </el-icon>
          </div>

          <div class="notification-content">
            <div class="notification-header">
              <h4 class="notification-title">
                {{ notification.title }}
                <span v-if="!notification.is_read" class="unread-badge"
                  >未读</span
                >
              </h4>
              <span class="notification-time">{{
                formatTime(notification.created_at)
              }}</span>
            </div>
            <p class="notification-text">{{ notification.content }}</p>
            <div class="notification-meta">
              <span class="notification-type">{{
                getNotificationTypeText(notification.type)
              }}</span>
              <span v-if="notification.read_at" class="read-time">
                已读于 {{ formatTime(notification.read_at) }}
              </span>
            </div>
          </div>

          <div class="notification-actions">
            <el-button
              v-if="!notification.is_read"
              text
              type="primary"
              @click.stop="markAsRead(notification.id)"
            >
              标记已读
            </el-button>
            <el-button
              text
              type="danger"
              @click.stop="deleteNotification(notification.id)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Bell,
  Refresh,
  Document,
  Warning,
  SuccessFilled,
  InfoFilled,
} from "@element-plus/icons-vue";
import { useNotificationStore } from "@/stores/notifications";
import { useTabs } from "@/composables/useTabs";
import { formatTime } from "@/utils/dateUtils";

const notificationStore = useNotificationStore();
const { openTab } = useTabs();

// 状态
const markingAllRead = ref(false);
const filters = reactive({
  is_read: "",
  type: "",
});

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 获取通知图标
const getNotificationIcon = (type) => {
  const iconMap = {
    contract_submitted: Document,
    contract_approved: SuccessFilled,
    contract_rejected: Warning,
    contract_assigned: Document,
    system_notice: InfoFilled,
  };
  return iconMap[type] || Bell;
};

// 获取通知图标颜色
const getNotificationIconColor = (type) => {
  const colorMap = {
    contract_submitted: "#409eff",
    contract_approved: "#67c23a",
    contract_rejected: "#f56c6c",
    contract_assigned: "#e6a23c",
    system_notice: "#909399",
  };
  return colorMap[type] || "#909399";
};

// 获取通知类型文本
const getNotificationTypeText = (type) => {
  const typeMap = {
    contract_submitted: "合同提交",
    contract_approved: "审核通过",
    contract_rejected: "审核拒绝",
    contract_assigned: "合同分配",
    system_notice: "系统通知",
  };
  return typeMap[type] || "未知类型";
};

// 刷新通知列表
const refreshNotifications = async () => {
  await fetchNotifications();
};

// 获取通知列表
const fetchNotifications = async () => {
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      // 将空字符串转换为undefined，避免发送空值到后端
      is_read: filters.is_read === "" ? undefined : filters.is_read,
      type: filters.type === "" ? undefined : filters.type,
    };

    const response = await notificationStore.fetchNotifications(params);
    if (response?.data) {
      pagination.total = response.data.total || 0;
    }
  } catch (error) {
    // 如果是取消请求的错误，不显示错误消息
    if (error.name === "CanceledError" || error.code === "ERR_CANCELED") {
      console.log("通知请求被取消，这是正常的");
      return;
    }
    console.error("获取通知列表失败:", error);
    ElMessage.error("获取通知列表失败");
  }
};

// 处理筛选变化
const handleFilterChange = () => {
  pagination.page = 1;
  fetchNotifications();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  fetchNotifications();
};

// 处理页大小变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchNotifications();
};

// 标记单个通知为已读
const markAsRead = async (id) => {
  try {
    await notificationStore.markAsRead(id);
    ElMessage.success("已标记为已读");
  } catch (error) {
    console.error("标记已读失败:", error);
    ElMessage.error("标记已读失败");
  }
};

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    markingAllRead.value = true;
    await notificationStore.markAllAsRead();
    ElMessage.success("所有通知已标记为已读");
    await fetchNotifications();
  } catch (error) {
    console.error("标记全部已读失败:", error);
    ElMessage.error("标记全部已读失败");
  } finally {
    markingAllRead.value = false;
  }
};

// 删除通知
const deleteNotification = async (id) => {
  try {
    await ElMessageBox.confirm("确定要删除这条通知吗？", "确认删除", {
      type: "warning",
    });

    await notificationStore.deleteNotification(id);
    ElMessage.success("通知已删除");
    await fetchNotifications();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除通知失败:", error);
      ElMessage.error("删除通知失败");
    }
  }
};

// 处理通知点击
const handleNotificationClick = async (notification) => {
  try {
    // 如果是未读通知，标记为已读
    if (!notification.is_read) {
      await notificationStore.markAsRead(notification.id);
    }

    // 根据通知类型进行相应的跳转
    if (notification.related_type === "contract" && notification.related_id) {
      // 跳转到合同详情
      openTab({
        key: `contract-detail-${notification.related_id}`,
        title: `合同详情`,
        path: `/contracts/${notification.related_id}`,
        component: "ContractDetailTab",
        icon: "Document",
        params: { contractId: notification.related_id },
      });
    }
  } catch (error) {
    console.error("处理通知点击失败:", error);
    ElMessage.error("操作失败");
  }
};

// 组件挂载
onMounted(() => {
  // 添加小延迟避免与TheLayout中的通知请求冲突
  setTimeout(() => {
    fetchNotifications();
  }, 100);
});
</script>

<style scoped>
.notifications-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-bar {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.notifications-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-text {
  margin-top: 16px;
  color: #909399;
  font-size: 16px;
}

.notifications-list {
  padding: 0;
}

.notification-card {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-card:hover {
  background-color: #f8f9fa;
}

.notification-card--unread {
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
}

.notification-card:last-child {
  border-bottom: none;
}

.notification-icon {
  margin-right: 16px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-badge {
  background: #409eff;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: normal;
}

.notification-time {
  color: #909399;
  font-size: 14px;
  white-space: nowrap;
}

.notification-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 8px 0;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.notification-type {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.notification-actions {
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pagination-container {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
</style>
