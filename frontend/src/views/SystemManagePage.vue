<template>
  <div class="system-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统管理</h1>
        <p class="page-subtitle">系统监控、数据统计和配置管理</p>
      </div>
      <div class="header-right">
        <el-button :loading="loading" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showBackupDialog">
          <el-icon><Download /></el-icon>
          数据备份
        </el-button>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon system">
                <el-icon :size="32"><Monitor /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">
                  {{ systemStats.version || "v1.0.0" }}
                </div>
                <div class="card-label">系统版本</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon uptime">
                <el-icon :size="32"><Timer /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">
                  {{ formatUptime(systemStats.uptime) }}
                </div>
                <div class="card-label">运行时间</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon memory">
                <el-icon :size="32"><Cpu /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">
                  {{ formatMemory(systemStats.memory?.used) }}
                </div>
                <div class="card-label">内存使用</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon storage">
                <el-icon :size="32"><FolderOpened /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">
                  {{ formatFileSize(systemStats.files?.total_size) }}
                </div>
                <div class="card-label">存储使用</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据统计 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span>用户统计</span>
                <el-button text @click="exportUsers">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>

            <div class="stats-content">
              <div class="stats-item">
                <span class="stats-label">总用户数</span>
                <span class="stats-value">{{
                  systemStats.users?.total || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">管理员</span>
                <span class="stats-value admin">{{
                  systemStats.users?.byRole?.admin || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">审核员</span>
                <span class="stats-value reviewer">{{
                  systemStats.users?.byRole?.reviewer || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">员工</span>
                <span class="stats-value employee">{{
                  systemStats.users?.byRole?.employee || 0
                }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span>合同统计</span>
                <el-button text @click="exportContracts">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>

            <div class="stats-content">
              <div class="stats-item">
                <span class="stats-label">总合同数</span>
                <span class="stats-value">{{
                  systemStats.contracts?.total || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">待审核</span>
                <span class="stats-value pending">{{
                  systemStats.contracts?.byStatus?.pending || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">审核中</span>
                <span class="stats-value reviewing">{{
                  systemStats.contracts?.byStatus?.reviewing || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">已通过</span>
                <span class="stats-value approved">{{
                  systemStats.contracts?.byStatus?.approved || 0
                }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">已拒绝</span>
                <span class="stats-value rejected">{{
                  systemStats.contracts?.byStatus?.rejected || 0
                }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 今日数据 -->
    <div class="today-section">
      <el-card class="today-card">
        <template #header>
          <div class="card-header">
            <span>今日数据</span>
            <span class="today-date">{{ formatDate(new Date()) }}</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="today-item">
              <div class="today-icon">
                <el-icon :size="24"><DocumentAdd /></el-icon>
              </div>
              <div class="today-info">
                <div class="today-value">
                  {{ systemStats.today?.total_today || 0 }}
                </div>
                <div class="today-label">新增合同</div>
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="today-item">
              <div class="today-icon">
                <el-icon :size="24"><Clock /></el-icon>
              </div>
              <div class="today-info">
                <div class="today-value">
                  {{ systemStats.today?.pending_today || 0 }}
                </div>
                <div class="today-label">待审核</div>
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="today-item">
              <div class="today-icon">
                <el-icon :size="24"><CircleCheckFilled /></el-icon>
              </div>
              <div class="today-info">
                <div class="today-value">
                  {{ systemStats.today?.approved_today || 0 }}
                </div>
                <div class="today-label">已审核</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 系统日志 -->
    <div class="logs-section">
      <el-card class="logs-card">
        <template #header>
          <div class="card-header">
            <span>系统日志</span>
            <div class="header-actions">
              <el-select
                v-model="logLevel"
                placeholder="日志级别"
                style="width: 120px"
                @change="loadLogs"
              >
                <el-option label="全部" value="all" />
                <el-option label="错误" value="error" />
                <el-option label="警告" value="warning" />
                <el-option label="信息" value="info" />
              </el-select>
              <el-button :loading="logsLoading" @click="loadLogs">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div class="logs-content">
          <div v-if="logs.length === 0" class="no-logs">
            <el-empty description="暂无日志数据" />
          </div>

          <div v-else class="logs-list">
            <div
              v-for="log in logs"
              :key="log.id"
              class="log-item"
              :class="`log-${log.level}`"
            >
              <div class="log-time">{{ formatDateTime(log.timestamp) }}</div>
              <div class="log-level">
                <el-tag :type="getLogLevelType(log.level)" size="small">
                  {{ log.level.toUpperCase() }}
                </el-tag>
              </div>
              <div class="log-message">{{ log.message }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 备份对话框 -->
    <el-dialog
      v-model="showBackup"
      title="数据备份"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="backup-content">
        <el-alert title="备份说明" type="info" :closable="false" show-icon>
          <template #default>
            <p>数据备份将包含以下内容：</p>
            <ul>
              <li>用户数据</li>
              <li>合同数据</li>
              <li>系统配置</li>
            </ul>
          </template>
        </el-alert>

        <div class="backup-options">
          <el-checkbox v-model="backupOptions.users">用户数据</el-checkbox>
          <el-checkbox v-model="backupOptions.contracts">合同数据</el-checkbox>
          <el-checkbox v-model="backupOptions.files">文件数据</el-checkbox>
        </div>
      </div>

      <template #footer>
        <el-button @click="showBackup = false">取消</el-button>
        <el-button
          type="primary"
          :loading="backupLoading"
          @click="performBackup"
        >
          开始备份
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Refresh,
  Download,
  Monitor,
  Timer,
  Cpu,
  FolderOpened,
  DocumentAdd,
  Clock,
  CircleCheckFilled,
} from "@element-plus/icons-vue";

import { adminAPI } from "@/api/users";
import { formatFileSize, formatDateTime } from "@/utils/uxUtils";

// 状态管理
const loading = ref(false);
const logsLoading = ref(false);
const backupLoading = ref(false);

// 系统统计数据
const systemStats = ref({});

// 日志相关
const logs = ref([]);
const logLevel = ref("all");

// 备份相关
const showBackup = ref(false);
const backupOptions = reactive({
  users: true,
  contracts: true,
  files: false,
});

// 格式化运行时间
const formatUptime = (seconds) => {
  if (!seconds) return "0秒";

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 格式化内存使用
const formatMemory = (bytes) => {
  if (!bytes) return "0 MB";
  return formatFileSize(bytes);
};

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleDateString("zh-CN");
};

// 获取日志级别类型
const getLogLevelType = (level) => {
  const types = {
    error: "danger",
    warning: "warning",
    info: "info",
    debug: "info",
  };
  return types[level] || "info";
};

// 显示备份对话框
const showBackupDialog = () => {
  showBackup.value = true;
};

// 加载系统统计
const loadSystemStats = async () => {
  try {
    loading.value = true;
    const response = await adminAPI.getSystemStats();

    if (response.success) {
      systemStats.value = response.data;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("加载系统统计失败:", error);
    }
    ElMessage.error("加载系统统计失败");
  } finally {
    loading.value = false;
  }
};

// 加载系统日志
const loadLogs = async () => {
  try {
    logsLoading.value = true;
    const response = await adminAPI.getSystemLogs({
      level: logLevel.value === "all" ? undefined : logLevel.value,
      page: 1,
      pageSize: 50,
    });

    if (response.success) {
      logs.value = response.data.data || [];
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("加载系统日志失败:", error);
    }
    ElMessage.error("加载系统日志失败");
  } finally {
    logsLoading.value = false;
  }
};

// 导出用户数据
const exportUsers = async () => {
  try {
    await ElMessageBox.confirm("确定要导出用户数据吗？", "确认导出", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info",
    });

    const response = await adminAPI.exportUsers();

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: "application/vnd.ms-excel",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `users_export_${Date.now()}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success("用户数据导出成功");
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("导出用户数据失败:", error);
      }
      ElMessage.error("导出用户数据失败");
    }
  }
};

// 导出合同数据
const exportContracts = async () => {
  try {
    await ElMessageBox.confirm("确定要导出合同数据吗？", "确认导出", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info",
    });

    const response = await adminAPI.exportContracts();

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: "application/vnd.ms-excel",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `contracts_export_${Date.now()}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success("合同数据导出成功");
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("导出合同数据失败:", error);
      }
      ElMessage.error("导出合同数据失败");
    }
  }
};

// 执行备份
const performBackup = async () => {
  try {
    backupLoading.value = true;

    const selectedOptions = Object.keys(backupOptions).filter(
      (key) => backupOptions[key],
    );
    if (selectedOptions.length === 0) {
      ElMessage.warning("请至少选择一项备份内容");
      return;
    }

    const response = await adminAPI.backup();

    if (response.success) {
      ElMessage.success("数据备份成功");
      showBackup.value = false;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("数据备份失败:", error);
    }
    ElMessage.error("数据备份失败");
  } finally {
    backupLoading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  await Promise.all([loadSystemStats(), loadLogs()]);
  ElMessage.success("数据已刷新");
};

// 组件挂载时加载数据
onMounted(() => {
  loadSystemStats();
  loadLogs();
});
</script>

<style scoped>
.system-manage-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.overview-section,
.stats-section,
.today-section,
.logs-section {
  padding: 24px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.card-icon.system {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.card-icon.uptime {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.card-icon.memory {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.card-icon.storage {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.stats-card {
  height: 280px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 200px;
  justify-content: space-around;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-label {
  font-size: 14px;
  color: #606266;
}

.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stats-value.admin {
  color: #f56c6c;
}

.stats-value.reviewer {
  color: #e6a23c;
}

.stats-value.employee {
  color: #409eff;
}

.stats-value.pending {
  color: #e6a23c;
}

.stats-value.reviewing {
  color: #409eff;
}

.stats-value.approved {
  color: #67c23a;
}

.stats-value.rejected {
  color: #f56c6c;
}

.today-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.today-card :deep(.el-card__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.today-card :deep(.el-card__body) {
  background: transparent;
}

.today-date {
  font-size: 14px;
  opacity: 0.8;
}

.today-item {
  display: flex;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.today-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.today-info {
  flex: 1;
}

.today-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
}

.today-label {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
}

.logs-card {
  min-height: 400px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.logs-content {
  max-height: 400px;
  overflow-y: auto;
}

.no-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #e4e7ed;
}

.log-item.log-error {
  border-left-color: #f56c6c;
}

.log-item.log-warning {
  border-left-color: #e6a23c;
}

.log-item.log-info {
  border-left-color: #409eff;
}

.log-time {
  font-size: 12px;
  color: #909399;
  min-width: 120px;
}

.log-level {
  min-width: 60px;
}

.log-message {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.backup-content {
  padding: 20px 0;
}

.backup-options {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-section .el-col {
    margin-bottom: 16px;
  }

  .stats-section .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .overview-section,
  .stats-section,
  .today-section,
  .logs-section {
    padding: 16px;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
  }

  .today-item {
    flex-direction: column;
    gap: 8px;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .log-time {
    min-width: auto;
  }
}
</style>
