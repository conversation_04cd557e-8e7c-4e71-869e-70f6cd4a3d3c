{"mcpServers": {"task-master-ai": {"type": "stdio", "command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"GOOGLE_API_KEY": "AIzaSyAcVlQrj0YPuRTEGtB-OzfI5RHiFKm08QE"}}, "Context 7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]}, "promptx": {"type": "stdio", "command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@alpha", "mcp-server"]}, "Playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "graphiti-memory": {"type": "stdio", "url": "http://localhost:8000/sse"}}}