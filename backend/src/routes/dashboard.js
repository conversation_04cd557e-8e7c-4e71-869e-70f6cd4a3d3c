/**
 * 首页数据路由
 * 提供不同角色的首页统计数据
 */

const express = require('express');
const router = express.Router();

const { ContractModel, UserModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { ResponseUtils, DateUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES, CONTRACT_STATUS } = require('../utils/constants');
const { responseCache, etagCache, cacheInvalidation } = require('../middleware/cache');
const { CacheHelpers } = require('../utils/cache');

/**
 * 获取首页统计数据
 * GET /api/dashboard/stats
 */
router.get('/stats',
  authenticateToken,
  responseCache({
    ttl: 300000, // 5分钟缓存
    type: 'statistics',
    keyGenerator: (req) => `dashboard_stats_${req.user.role}_${req.user.id}`,
    shouldCache: (req, res) => req.method === 'GET' && res.statusCode === 200
  }),
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let stats = {};

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          stats = await getEmployeeStats(req.user.id);
          break;
        case USER_ROLES.REVIEWER:
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          stats = await getReviewerStats(req.user.id);
          break;
        case USER_ROLES.ADMIN:
          stats = await getAdminStats();
          break;
        default:
          stats = {};
      }

      res.json(ResponseUtils.success(stats));

    } catch (error) {
      console.error('获取首页统计数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取最近活动
 * GET /api/dashboard/activities
 */
router.get('/activities',
  authenticateToken,
  responseCache({
    ttl: 180000, // 3分钟缓存
    type: 'statistics',
    keyGenerator: (req) => `dashboard_activities_${req.user.role}_${req.user.id}_${req.query.limit || 10}`,
    shouldCache: (req, res) => req.method === 'GET' && res.statusCode === 200
  }),
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const limit = parseInt(req.query.limit) || 10;
      let activities = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          activities = await getEmployeeActivities(req.user.id, limit);
          break;
        case USER_ROLES.REVIEWER:
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          activities = await getReviewerActivities(req.user.id, limit);
          break;
        case USER_ROLES.ADMIN:
          activities = await getAdminActivities(limit);
          break;
      }

      res.json(ResponseUtils.success(activities));

    } catch (error) {
      console.error('获取最近活动错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取快捷操作
 * GET /api/dashboard/quick-actions
 */
router.get('/quick-actions',
  authenticateToken,
  responseCache({
    ttl: 600000, // 10分钟缓存 (快捷操作变化较少)
    type: 'statistics',
    keyGenerator: (req) => `dashboard_quick_actions_${req.user.role}`,
    shouldCache: (req, res) => req.method === 'GET' && res.statusCode === 200
  }),
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let quickActions = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          quickActions = getEmployeeQuickActions();
          break;
        case USER_ROLES.REVIEWER:
          quickActions = getReviewerQuickActions();
          break;
        case USER_ROLES.ADMIN:
          quickActions = getAdminQuickActions();
          break;
      }

      res.json(ResponseUtils.success(quickActions));

    } catch (error) {
      console.error('获取快捷操作错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取通知信息
 * GET /api/dashboard/notifications
 */
router.get('/notifications',
  authenticateToken,
  responseCache({
    ttl: 120000, // 2分钟缓存 (通知需要较快更新)
    type: 'statistics',
    keyGenerator: (req) => `dashboard_notifications_${req.user.role}_${req.user.id}`,
    shouldCache: (req, res) => req.method === 'GET' && res.statusCode === 200
  }),
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let notifications = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          notifications = await getEmployeeNotifications(req.user.id);
          break;
        case USER_ROLES.REVIEWER:
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          notifications = await getReviewerNotifications(req.user.id);
          break;
        case USER_ROLES.ADMIN:
          notifications = await getAdminNotifications();
          break;
      }

      res.json(ResponseUtils.success(notifications));

    } catch (error) {
      console.error('获取通知信息错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取用户个人统计数据
 * GET /api/dashboard/user-stats
 */
router.get('/user-stats',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const userId = req.user.id;
      let userStats = {};

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          userStats = await getUserEmployeeStats(userId);
          break;
        case USER_ROLES.REVIEWER:
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          userStats = await getUserReviewerStats(userId);
          break;
        case USER_ROLES.ADMIN:
          userStats = await getUserAdminStats();
          break;
        default:
          userStats = {};
      }

      res.json(ResponseUtils.success(userStats));

    } catch (error) {
      console.error('获取用户个人统计数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

// ==================== 员工相关统计 ====================

/**
 * 获取员工统计数据
 */
async function getEmployeeStats(userId) {
  // 获取合同统计
  const contractStats = await ContractModel.getStats({ submitter_id: userId });

  // 获取本月提交数量
  const thisMonthCount = await getThisMonthSubmissions(userId);

  // 获取平均审核时间
  const avgReviewTime = await getAverageReviewTime(userId);

  return {
    contracts: contractStats,
    thisMonth: thisMonthCount,
    averageReviewTime: avgReviewTime,
    summary: {
      totalSubmitted: contractStats.total,
      pendingReview: contractStats.pending, // reviewing已经合并到pending中
      approved: contractStats.approved,
      rejected: contractStats.rejected
    }
  };
}

/**
 * 获取员工最近活动
 */
async function getEmployeeActivities(userId, limit) {
  const contracts = await ContractModel.getList(
    { submitter_id: userId },
    1,
    limit
  );

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'contract',
    action: getContractAction(contract.status),
    title: `合同 ${contract.serial_number}`,
    description: contract.filename,
    status: contract.status,
    time: contract.updated_at || contract.created_at
  }));
}

// ==================== 审核员相关统计 ====================

/**
 * 获取审核员统计数据
 */
async function getReviewerStats(userId) {
  // 获取分配给该审核员的合同统计
  const contractStats = await ContractModel.getStats({ reviewer_id: userId });

  // 获取待审核数量
  const pendingCount = await getPendingReviewCount(userId);

  // 获取本月审核数量
  const thisMonthReviewed = await getThisMonthReviewed(userId);

  // 获取平均审核时间
  const avgReviewTime = await getReviewerAverageTime(userId);

  return {
    contracts: contractStats,
    pending: pendingCount,
    thisMonthReviewed: thisMonthReviewed,
    averageReviewTime: avgReviewTime,
    summary: {
      totalAssigned: contractStats.total,
      pendingReview: contractStats.pending,
      completed: contractStats.approved + contractStats.rejected
    }
  };
}

/**
 * 获取审核员最近活动
 */
async function getReviewerActivities(userId, limit) {
  const contracts = await ContractModel.getList(
    { reviewer_id: userId },
    1,
    limit
  );

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'review',
    action: getReviewAction(contract.status),
    title: `审核合同 ${contract.serial_number}`,
    description: `提交人: ${contract.submitter_name}`,
    status: contract.status,
    time: contract.reviewed_at || contract.updated_at || contract.created_at
  }));
}

// ==================== 管理员相关统计 ====================

/**
 * 获取管理员统计数据
 */
async function getAdminStats() {
  // 获取全部合同统计
  const contractStats = await ContractModel.getStats();

  // 获取用户统计
  const userStats = await getUserStats();

  // 获取本月统计
  const thisMonthStats = await getThisMonthStats();

  // 获取系统概览
  const systemOverview = await getSystemOverview();

  return {
    contracts: contractStats,
    users: userStats,
    thisMonth: thisMonthStats,
    system: systemOverview,
    summary: {
      totalContracts: contractStats.total,
      totalUsers: userStats.total,
      activeUsers: userStats.active,
      systemHealth: 'good'
    }
  };
}

/**
 * 获取管理员最近活动
 */
async function getAdminActivities(limit) {
  const contracts = await ContractModel.getList({}, 1, limit);

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'system',
    action: getSystemAction(contract.status),
    title: `合同 ${contract.serial_number}`,
    description: `${contract.submitter_name} → ${contract.reviewer_name}`,
    status: contract.status,
    time: contract.updated_at || contract.created_at
  }));
}

// ==================== 快捷操作定义 ====================

function getEmployeeQuickActions() {
  return [
    {
      key: 'submit-contract',
      title: '提交合同',
      description: '上传新的合同文件进行审核',
      icon: 'Upload',
      color: 'primary',
      path: '/submit'
    },
    {
      key: 'my-contracts',
      title: '我的合同',
      description: '查看我提交的所有合同',
      icon: 'Document',
      color: 'success',
      path: '/my-contracts'
    },
    {
      key: 'profile',
      title: '个人设置',
      description: '修改个人信息和密码',
      icon: 'User',
      color: 'info',
      path: '/profile'
    }
  ];
}

function getReviewerQuickActions() {
  return [
    {
      key: 'pending-review',
      title: '待审核',
      description: '查看分配给我的待审核合同',
      icon: 'Timer',
      color: 'warning',
      path: '/pending-review'
    },

    {
      key: 'reviewed',
      title: '已审核',
      description: '查看已完成审核的合同',
      icon: 'Check',
      color: 'success',
      path: '/reviewed'
    }
  ];
}

function getAdminQuickActions() {
  return [
    {
      key: 'user-management',
      title: '用户管理',
      description: '管理系统用户和权限',
      icon: 'UserFilled',
      color: 'primary',
      path: '/user-management'
    },
    {
      key: 'contract-management',
      title: '合同管理',
      description: '查看和管理所有合同',
      icon: 'FolderOpened',
      color: 'success',
      path: '/contract-management'
    },
    {
      key: 'system-stats',
      title: '系统统计',
      description: '查看系统使用统计',
      icon: 'DataBoard',
      color: 'info',
      path: '/system-stats'
    }
  ];
}

// ==================== 辅助函数 ====================

async function getThisMonthSubmissions(userId) {
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  // 这里简化实现，实际应该使用 SQL 查询
  const contracts = await ContractModel.getList({ submitter_id: userId }, 1, 1000);
  return contracts.data.filter(contract =>
    new Date(contract.created_at) >= startOfMonth
  ).length;
}

async function getAverageReviewTime(userId) {
  // 简化实现，返回模拟数据
  return '2.5天';
}

async function getPendingReviewCount(userId) {
  const result = await ContractModel.getStats({
    reviewer_id: userId,
    status: CONTRACT_STATUS.PENDING
  });
  return result.pending || 0;
}

async function getThisMonthReviewed(userId) {
  // 简化实现，返回模拟数据
  return 15;
}

async function getReviewerAverageTime(userId) {
  // 简化实现，返回模拟数据
  return '1.8天';
}

async function getUserStats() {
  const users = await UserModel.getList({}, 1, 1000);
  const total = users.pagination.total;
  const active = users.data.filter(user => user.status === 'active').length;

  return {
    total,
    active,
    employees: users.data.filter(user => user.role === USER_ROLES.EMPLOYEE).length,
    reviewers: users.data.filter(user => user.role === USER_ROLES.REVIEWER).length,
    admins: users.data.filter(user => user.role === USER_ROLES.ADMIN).length
  };
}

async function getThisMonthStats() {
  // 简化实现，返回模拟数据
  return {
    contracts: 25,
    users: 3,
    reviews: 20
  };
}

async function getSystemOverview() {
  return {
    uptime: '15天',
    version: '1.0.0',
    lastBackup: '2024-01-15',
    diskUsage: '45%'
  };
}

async function getEmployeeNotifications(userId) {
  // 简化实现，返回模拟通知
  return [
    {
      id: 1,
      type: 'info',
      title: '合同审核完成',
      message: '您的合同 HT001 已审核通过',
      time: new Date().toISOString(),
      read: false
    }
  ];
}

async function getReviewerNotifications(userId) {
  const pendingCount = await getPendingReviewCount(userId);
  const notifications = [];

  if (pendingCount > 0) {
    notifications.push({
      id: 1,
      type: 'warning',
      title: '待审核合同',
      message: `您有 ${pendingCount} 个合同待审核`,
      time: new Date().toISOString(),
      read: false
    });
  }

  return notifications;
}

async function getAdminNotifications() {
  // 简化实现，返回模拟通知
  return [
    {
      id: 1,
      type: 'success',
      title: '系统运行正常',
      message: '所有服务运行正常',
      time: new Date().toISOString(),
      read: true
    }
  ];
}

function getContractAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '已提交',
    [CONTRACT_STATUS.REVIEWING]: '审核中',
    [CONTRACT_STATUS.APPROVED]: '已通过',
    [CONTRACT_STATUS.REJECTED]: '已拒绝'
  };
  return actions[status] || '未知';
}

function getReviewAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '待审核',
    [CONTRACT_STATUS.REVIEWING]: '审核中',
    [CONTRACT_STATUS.APPROVED]: '审核通过',
    [CONTRACT_STATUS.REJECTED]: '审核拒绝'
  };
  return actions[status] || '未知';
}

function getSystemAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '新合同提交',
    [CONTRACT_STATUS.APPROVED]: '合同审核通过',
    [CONTRACT_STATUS.REJECTED]: '合同审核拒绝'
  };
  return actions[status] || '系统活动';
}

// ==================== 用户个人统计函数 ====================

/**
 * 获取员工个人统计数据
 */
async function getUserEmployeeStats(userId) {
  const contractStats = await ContractModel.getStats({ submitter_id: userId });

  return {
    totalContracts: contractStats.total || 0,
    pendingContracts: contractStats.pending || 0,
    approvedContracts: contractStats.approved || 0,
    rejectedContracts: contractStats.rejected || 0,
    totalReviewed: 0,
    pendingReview: 0,
    approvedReview: 0,
    rejectedReview: 0,
  };
}

/**
 * 获取审核员个人统计数据
 */
async function getUserReviewerStats(userId) {
  const contractStats = await ContractModel.getStats({ reviewer_id: userId });

  return {
    totalContracts: 0,
    pendingContracts: 0,
    approvedContracts: 0,
    rejectedContracts: 0,
    totalReviewed: contractStats.total || 0,
    pendingReview: contractStats.pending || 0,
    approvedReview: contractStats.approved || 0,
    rejectedReview: contractStats.rejected || 0,
  };
}

/**
 * 获取管理员个人统计数据
 */
async function getUserAdminStats() {
  const contractStats = await ContractModel.getStats({});

  return {
    totalContracts: contractStats.total || 0,
    pendingContracts: contractStats.pending || 0,
    approvedContracts: contractStats.approved || 0,
    rejectedContracts: contractStats.rejected || 0,
    totalReviewed: 0,
    pendingReview: 0,
    approvedReview: 0,
    rejectedReview: 0,
  };
}

module.exports = router;
